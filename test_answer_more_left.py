#!/usr/bin/env python3
"""
测试答案图片更左对齐效果
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_more_left():
    """测试答案图片更左对齐效果"""
    
    print("🔍 测试答案图片更左对齐效果...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 答案图片更左对齐测试

## 对齐效果对比

计算：2 + 3 = ?

/
这是一个简单的加法题目。

答案是5。

现在的"答案"图片应该贴住答案框的左边框，比之前更靠左。
/

## 详细说明

/
**对齐位置变化：**

1. **最初：** 答案图片居中显示
2. **第一次修改：** 答案图片左对齐到内容区域
3. **第二次修改：** 答案图片更左，贴住边框

**当前效果：**
- 答案图片现在贴住答案框的左边框
- 比内容文字更靠左一些
- 视觉上更加突出

这样的答案图片位置应该满足"再往左点"的要求。
/

## 多个答案测试

第一题：1 + 1 = ?

/
简单的加法运算。

答案：2
/

第二题：3 × 4 = ?

/
乘法运算。

答案：12
/

第三题：10 ÷ 2 = ?

/
除法运算。

答案：5
/

## 长文本测试

复杂计算：(5 + 3) × 2 - 4 ÷ 2 = ?

/
这道题需要按照运算顺序来计算：

**第一步：** 计算括号
(5 + 3) = 8

**第二步：** 计算乘除法（从左到右）
8 × 2 = 16
4 ÷ 2 = 2

**第三步：** 计算减法
16 - 2 = 14

**最终答案：** 14

所有的"答案"图片都应该贴住答案框的左边框，比文字内容更靠左。

这样的布局让答案图片更加醒目，同时保持了整体的视觉协调性。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成答案更左对齐测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_more_left.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF中的答案图片对齐效果:")
                print("1. ✅ 答案图片应该贴住答案框的左边框")
                print("2. ✅ 答案图片比内容文字更靠左")
                print("3. ✅ 答案图片不在内容区域内，而是在边框位置")
                print("4. ✅ 所有答案图片都保持这种更左的对齐")
                
                print("\n对齐位置变化:")
                print("- 🔄 第一版：答案图片居中显示")
                print("- 📍 第二版：答案图片左对齐到内容区域")
                print("- ✅ 第三版：答案图片贴住边框，更靠左")
                
                print("\n技术实现:")
                print("- 修改前：img_x = content_x (内容区域左边界)")
                print("- 修改后：img_x = self.border_width (边框位置)")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_more_left())
