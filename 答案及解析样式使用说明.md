# 答案及解析样式使用说明

## 概述

我已经为你的PDF生成系统添加了一个新的"答案及解析"内容样式。这个样式具有以下特点：

- **独立成段**：不会与其他内容混合使用
- **特殊背景**：使用颜色为 `#fffde5` 的浅黄色背景
- **橙色边框**：1mm宽度的 `#f7ab00` 橙色描边
- **圆角矩形**：8px圆角半径的矩形边框
- **自动换行**：内容能够自动换行
- **自适应高度**：矩形能够根据内容自动调整高度
- **楷体字体**：使用楷体字体显示内容

## 使用方法

### 基本语法

在Markdown文档中，使用斜杠 `/` 包围需要应用"答案及解析"样式的内容：

#### 单行格式
```markdown
/这里是答案及解析的内容/
```

#### 多行格式
```markdown
/
这里是第一段内容。

这里是第二段内容，支持多段落。

这里是第三段内容，所有内容都会在同一个圆角矩形框内。
/
```

### 示例

#### 单行答案示例
```markdown
# 数学练习题

## 第一题

计算：2 + 3 × 4 = ?

/这是一个关于运算顺序的题目。根据数学运算法则，乘法的优先级高于加法，所以应该先计算3×4=12，然后计算2+12=14。因此答案是14。/
```

#### 多段落答案示例
```markdown
## 第二题

如果一个班级有30名学生，其中男生占40%，女生占60%，请分别计算男生和女生的人数。

/
这是一个百分比应用题，需要分别计算男生和女生的人数。

**男生人数计算：**
男生人数 = 总人数 × 男生比例 = 30 × 40% = 12人

**女生人数计算：**
女生人数 = 总人数 × 女生比例 = 30 × 60% = 18人

**验证：** 12 + 18 = 30 ✓

这道题考查学生对百分比概念的理解和计算能力。
/
```

#### 图片支持示例
```markdown
## 第三题

计算下图长方形的面积：

/
根据题目给出的长方形尺寸：

![长方形图](images/rectangle.png?size=medium)

**面积计算：**
面积 = 长 × 宽 = 8cm × 5cm = 40平方厘米

**答案：** 40平方厘米

这道题考查学生对长方形面积公式的应用。
/
```

#### 图片尺寸调整示例
```markdown
## 第四题

观察不同尺寸的图片显示效果：

/
**小尺寸图标：**
![小图标](images/icon.png?size=small)

**中等尺寸图片：**
![教学图片](images/diagram.png?size=medium)

**大尺寸图表：**
![详细图表](images/chart.png?size=large)

**指定宽度：**
![指定宽度](images/example.png?width=200)

不同的尺寸参数可以满足各种显示需求。
/
```

## 技术实现

### 核心类：AnswerAnalysisBox

这个样式通过自定义的 `AnswerAnalysisBox` Flowable类实现，具有以下特性：

1. **自适应尺寸**：`wrap()` 方法根据文本内容计算所需的高度
2. **圆角矩形绘制**：使用ReportLab的 `roundRect()` 方法绘制背景
3. **文本渲染**：支持Markdown格式的文本内容
4. **内边距控制**：15px的内边距确保文本不会贴边

### 颜色配置

- **背景色**：`#fffde5` (RGB: 255, 253, 229)
- **边框色**：`#f7ab00` (RGB: 247, 171, 0)
- **边框宽度**：1mm (约2.83点)
- **圆角半径**：8px

### 字体设置

- 优先使用系统楷体字体
- 如果楷体不可用，降级到中文字体
- 最终降级到默认字体

## 功能特性

### 🎯 多段落支持
- ✅ 单行内容：`/简短答案/`
- ✅ 多行内容：跨越多行的长答案
- ✅ 多段落内容：支持段落分隔和格式化
- ✅ 图片支持：支持在答案框中显示图片
- ✅ 图文混合：文本和图片可以混合排列
- ✅ 自动高度调整：根据内容自动调整框的高度

### 📝 格式支持
- ✅ **粗体文本**：`**文本**`
- ✅ *斜体文本*：`*文本*`
- ✅ 双括号特殊格式：`（（文本））`
- ✅ 行内代码：`` `代码` ``
- ✅ 图片显示：`![描述](图片路径)`
- ✅ 图片尺寸调整：`![描述](路径?size=medium)`
- ✅ 精确尺寸控制：`![描述](路径?width=200)`
- ✅ 自动换行：长文本自动换行

### 🎨 视觉效果
- ✅ 圆角矩形背景
- ✅ 橙色边框突出显示
- ✅ 楷体字体（如果可用）
- ✅ 图片居中对齐
- ✅ 图片自动缩放适应
- ✅ 适当的内边距和间距

## 注意事项

1. **格式要求**：
   - 单行格式：`/内容/` 必须在同一行
   - 多行格式：以 `/` 开始，以 `/` 结束，中间可以有多行内容

2. **段落分隔**：
   - 在多行格式中，空行会被识别为段落分隔
   - 每个段落会保持适当的间距

3. **内容处理**：
   - 支持Markdown格式（粗体、斜体等）
   - 支持图片显示（本地图片和网络图片）
   - 支持图片尺寸调整（预设和精确控制）
   - 自动处理双括号特殊格式
   - 长文本自动换行
   - 图片自动缩放和居中对齐

4. **兼容性**：
   - 与其他样式完全兼容
   - 可以在同一文档中混合使用

## 测试验证

运行以下命令可以生成测试PDF：

```bash
python test_answer_analysis.py
```

这将生成一个包含多个答案及解析框的测试PDF文件，用于验证样式效果。

## 与其他样式的兼容性

这个新样式与现有的其他样式完全兼容：

- ✅ 一级标题（带背景图片）
- ✅ 编号列表（带圆形背景）
- ✅ 双括号文本（橙色楷体）
- ✅ 普通段落和其他Markdown元素

所有样式可以在同一个文档中混合使用，不会产生冲突。

## 图片尺寸调整功能

### 支持的尺寸参数

| 参数 | 说明 | 示例 | 效果 |
|------|------|------|------|
| `size=small` | 小尺寸 | `![图](path?size=small)` | 适合图标、简单示意图 |
| `size=medium` | 中等尺寸 | `![图](path?size=medium)` | 适合一般教学图片（推荐） |
| `size=large` | 大尺寸 | `![图](path?size=large)` | 适合重要图表、复杂图形 |
| `size=original` | 原始尺寸 | `![图](path?size=original)` | 保持原始比例 |
| `width=数值` | 指定宽度 | `![图](path?width=200)` | 设置具体宽度（像素） |
| `height=数值` | 指定高度 | `![图](path?height=150)` | 设置具体高度（像素） |

### 使用建议

1. **一般情况**：使用 `size=medium`，适合大多数场景
2. **图标类**：使用 `size=small`，节省空间
3. **重要图表**：使用 `size=large`，突出显示
4. **特殊需求**：使用 `width` 或 `height` 精确控制
5. **避免变形**：尽量不要同时指定宽度和高度

### 示例对比

```markdown
/
**不同尺寸效果对比：**

小尺寸：![小图](image.png?size=small)
中等尺寸：![中图](image.png?size=medium)
大尺寸：![大图](image.png?size=large)

**精确控制：**
指定宽度：![宽度](image.png?width=180)
指定高度：![高度](image.png?height=120)
/
```
