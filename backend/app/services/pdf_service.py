"""
PDF生成服务
使用ReportLab生成真正的PDF文件
"""

import os
import uuid
import base64
import markdown
from typing import Optional, List, Dict, Any
import asyncio
import time
from io import BytesIO
import aiohttp
import aiofiles
from urllib.parse import urlparse, urljoin
from PIL import Image as PILImage

from reportlab.lib.pagesizes import A4, A3, letter, legal
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, Image, PageTemplate, Frame, Flowable
from reportlab.platypus.doctemplate import BaseDocTemplate
from reportlab.lib import colors
from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase.cidfonts import UnicodeCIDFont
import re
import platform

from app.models.schemas import LayoutConfig
from app.core.config import settings


class ImageBackgroundHeading(Flowable):
    """带背景图片的一级标题"""

    def __init__(self, text, style, background_image_path=None):
        self.text = text
        self.style = style
        self.background_image_path = background_image_path
        self.width = 0
        self.height = 80  # 默认高度

    def wrap(self, availWidth, availHeight):
        """计算所需的宽度和高度"""
        self.width = availWidth
        # 固定背景图片高度为-55px
        self.height = -55
        return (self.width, self.height)

    def draw(self):
        """绘制带背景图片的标题"""
        canvas = self.canv

        # 保存画布状态
        canvas.saveState()

        try:
            # 绘制背景图片
            if self.background_image_path and os.path.exists(self.background_image_path):
                # 固定背景图片尺寸 (340px宽 x 55px高)
                fixed_img_width = 340
                fixed_img_height = 55

                # 计算居中位置
                img_x = (self.width - fixed_img_width) / 2
                img_y = (self.height - fixed_img_height) / 2

                # 绘制背景图片
                canvas.drawImage(
                    self.background_image_path,
                    img_x, img_y,
                    width=fixed_img_width,
                    height=fixed_img_height,
                    preserveAspectRatio=False,
                    mask='auto'
                )
            else:
                # 如果没有背景图片，使用原来的背景色和边框
                canvas.setFillColor(self.style.backColor or colors.Color(0.95, 0.95, 0.95))
                canvas.setStrokeColor(self.style.borderColor or colors.Color(0.8, 0.8, 0.8))
                canvas.setLineWidth(self.style.borderWidth or 1)

                # 绘制圆角矩形背景
                canvas.roundRect(
                    20, 10,
                    self.width - 40, self.height - 20,
                    radius=10,
                    fill=1, stroke=1
                )

            # 绘制文字
            canvas.setFillColor(self.style.textColor or colors.white)
            canvas.setFont(self.style.fontName, self.style.fontSize)

            # 计算文字位置（居中）
            text_width = canvas.stringWidth(self.text, self.style.fontName, self.style.fontSize)
            text_x = (self.width - text_width) / 2
            text_y = (self.height - self.style.fontSize) / 2

            # 绘制文字
            canvas.drawString(text_x, text_y, self.text)

        except Exception as e:
            print(f"绘制背景图片标题失败: {e}")
            # 降级到普通文字
            canvas.setFillColor(colors.black)
            canvas.setFont(self.style.fontName, self.style.fontSize)
            text_width = canvas.stringWidth(self.text, self.style.fontName, self.style.fontSize)
            text_x = (self.width - text_width) / 2
            text_y = (self.height - self.style.fontSize) / 2
            canvas.drawString(text_x, text_y, self.text)

        # 恢复画布状态
        canvas.restoreState()


class NumberedListItem(Flowable):
    """带背景图片的编号列表项"""

    def __init__(self, number, text, style, background_image_path=None):
        self.number = number
        self.text = text
        self.style = style
        self.background_image_path = background_image_path
        self.width = 0
        self.height = 30  # 默认高度

    def wrap(self, availWidth, availHeight):
        """计算所需的宽度和高度"""
        self.width = availWidth
        # 根据文本内容计算高度
        text_height = self.style.fontSize * 1.2
        self.height = max(30, text_height + 10)  # 最小30px，根据文本调整
        return (self.width, self.height)

    def draw(self):
        """绘制带背景图片的编号列表项"""
        canvas = self.canv

        # 保存画布状态
        canvas.saveState()

        try:
            # 圆形背景参数
            circle_diameter = 20  # 20px直径
            circle_radius = circle_diameter / 2
            circle_x = 10  # 距离左边10px（向左移动5px）
            circle_y = self.height / 2  # 垂直居中

            # 尝试绘制背景图片
            if self.background_image_path and os.path.exists(self.background_image_path):
                # 绘制背景图片
                img_x = circle_x - circle_radius
                img_y = circle_y - circle_radius
                canvas.drawImage(
                    self.background_image_path,
                    img_x, img_y,
                    width=circle_diameter,
                    height=circle_diameter,
                    preserveAspectRatio=True,
                    mask='auto'
                )
            else:
                # 如果没有背景图片，绘制橙色圆形背景
                canvas.setFillColor(colors.Color(1.0, 0.549, 0.0))  # #FF8C00 橙色
                canvas.circle(circle_x, circle_y, circle_radius, fill=1, stroke=0)

            # 绘制编号文字（白色）
            canvas.setFillColor(colors.white)
            canvas.setFont(self.style.fontName, self.style.fontSize * 0.8)  # 稍小的字体适应圆形
            number_text = str(self.number)
            text_width = canvas.stringWidth(number_text, self.style.fontName, self.style.fontSize * 0.8)
            text_x = circle_x - text_width/1  # 文字居中
            text_y = circle_y - self.style.fontSize * 0.4  # 垂直居中调整
            canvas.drawString(text_x, text_y, number_text)

            # 绘制列表项文字（黑色）
            canvas.setFillColor(colors.black)
            canvas.setFont(self.style.fontName, self.style.fontSize)
            text_x = circle_x + circle_radius + 15  # 圆形右侧15px处开始
            text_y = circle_y - self.style.fontSize * 0.3  # 与圆形对齐
            canvas.drawString(text_x, text_y, self.text)

        except Exception as e:
            print(f"绘制编号列表项失败: {e}")
            # 降级到普通文字
            canvas.setFillColor(colors.black)
            canvas.setFont(self.style.fontName, self.style.fontSize)
            canvas.drawString(20, self.height / 2 - self.style.fontSize / 2, f"{self.number}. {self.text}")

        # 恢复画布状态
        canvas.restoreState()


class AnswerAnalysisBox(Flowable):
    """答案及解析内容框 - 带圆角矩形背景的自适应高度文本框，支持多段落"""

    def __init__(self, text, style):
        Flowable.__init__(self)
        self.text = text
        self.style = style
        self.width = 0
        self.height = 0
        self.padding = 15  # 内边距15px
        self.border_width = 1  # 1mm边框宽度，转换为点数约2.83点
        self.corner_radius = 8  # 圆角半径

        # 颜色定义
        self.bg_color = colors.Color(1.0, 0.992, 0.898)  # #fffde5
        self.border_color = colors.Color(0.969, 0.671, 0.0)  # #f7ab00

    def wrap(self, availWidth, availHeight):
        """计算所需的宽度和高度"""
        self.width = availWidth

        # 计算文本区域的可用宽度（减去内边距和边框）
        text_width = availWidth - (self.padding * 2) - (self.border_width * 2)

        # 处理多段落内容，支持图片
        from reportlab.platypus import Paragraph

        # 分割文本为段落
        paragraphs = self.text.split('\n')
        self.content_objects = []  # 存储段落和图片对象
        total_height = 0

        for para_text in paragraphs:
            para_text = para_text.strip()
            if para_text:  # 跳过空段落
                # 检查是否包含图片（支持尺寸参数）
                img_match = re.search(r'!\[(.*?)\]\((.*?)\)', para_text)

                if img_match:
                    # 处理包含图片的段落
                    alt_text = img_match.group(1)
                    img_src_full = img_match.group(2)

                    # 解析图片路径和尺寸参数
                    img_src, size_params = self._parse_image_size(img_src_full)

                    # 分割图片前后的文本
                    before_img = para_text[:img_match.start()].strip()
                    after_img = para_text[img_match.end():].strip()

                    # 添加图片前的文本
                    if before_img:
                        before_text = self._process_inline_markdown(before_img)
                        para = Paragraph(before_text, self.style)
                        para_width, para_height = para.wrap(text_width, availHeight)
                        self.content_objects.append(('paragraph', para))
                        total_height += para_height + (self.style.spaceAfter or 6)

                    # 处理图片
                    img_element = self._process_image_for_answer_box(img_src, alt_text, text_width, size_params)
                    if img_element:
                        self.content_objects.append(('image', img_element))
                        # 获取图片高度（ReportLab Image对象使用drawHeight属性）
                        img_height = getattr(img_element, 'drawHeight', getattr(img_element, '_height', 100))
                        total_height += img_height + 10  # 图片间距

                    # 添加图片后的文本
                    if after_img:
                        after_text = self._process_inline_markdown(after_img)
                        para = Paragraph(after_text, self.style)
                        para_width, para_height = para.wrap(text_width, availHeight)
                        self.content_objects.append(('paragraph', para))
                        total_height += para_height + (self.style.spaceAfter or 6)

                else:
                    # 普通文本段落 - 检查是否包含"答案"需要替换为图片
                    if '答案' in para_text:
                        # 处理包含"答案"的文本，替换为图片
                        text_parts = self._process_text_with_answer_replacement(para_text)

                        for part_type, part_content in text_parts:
                            if part_type == 'text' and part_content.strip():
                                # 文本部分
                                para = Paragraph(part_content, self.style)
                                para_width, para_height = para.wrap(text_width, availHeight)
                                self.content_objects.append(('paragraph', para))
                                total_height += para_height + (self.style.spaceAfter or 6)

                            elif part_type == 'answer_image':
                                # 答案图片部分
                                answer_img = self._create_answer_image(text_width)
                                if answer_img:
                                    self.content_objects.append(('image', answer_img))
                                    img_height = getattr(answer_img, 'drawHeight', getattr(answer_img, '_height', 30))
                                    total_height += img_height + 5  # 答案图片间距
                    else:
                        # 普通文本段落 - 只处理Markdown格式
                        para_text = self._process_inline_markdown(para_text)
                        para = Paragraph(para_text, self.style)
                        para_width, para_height = para.wrap(text_width, availHeight)

                        self.content_objects.append(('paragraph', para))
                        total_height += para_height + (self.style.spaceAfter or 6)

        # 计算总高度（内容高度 + 内边距 + 边框）
        self.height = total_height + (self.padding * 2) + (self.border_width * 2)

        # 限制最大高度，避免超出页面
        max_allowed_height = availHeight * 0.8  # 不超过可用高度的80%
        if self.height > max_allowed_height:
            self.height = max_allowed_height

        return (self.width, self.height)

    def _process_inline_markdown(self, text: str) -> str:
        """处理行内Markdown格式"""
        # 双括号文本 - 橘色楷体文字，保留一个括号
        kaiti_font = self.style.fontName  # 使用当前样式的字体
        text = re.sub(r'（（(.*?)））', rf'<font color="#FF8C00" name="{kaiti_font}">（\1）</font>', text)

        # 粗体
        text = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', text)
        text = re.sub(r'__(.*?)__', r'<b>\1</b>', text)

        # 斜体
        text = re.sub(r'\*(.*?)\*', r'<i>\1</i>', text)
        text = re.sub(r'_(.*?)_', r'<i>\1</i>', text)

        # 行内代码
        text = re.sub(r'`(.*?)`', r'<font name="Courier">\1</font>', text)

        return text

    def _process_text_with_answer_replacement(self, text: str) -> list:
        """处理文本，将"答案"替换为图片，返回文本片段和图片的混合列表"""
        parts = []

        # 分割文本，找到"答案"的位置
        segments = text.split('答案')

        for i, segment in enumerate(segments):
            if segment:  # 添加非空文本段
                # 处理Markdown格式
                processed_segment = self._process_inline_markdown(segment)
                parts.append(('text', processed_segment))

            # 在除了最后一个段落之外的每个段落后添加答案图片
            if i < len(segments) - 1:
                parts.append(('answer_image', None))

        return parts

    def _create_answer_image(self, max_width: float) -> Optional[Image]:
        """创建答案标签图片"""
        try:
            # 查找答案图片路径（考虑不同的工作目录）
            answer_image_paths = [
                "answer_images/answer_label.png",
                "answer_images/answer_label_small.png",
                "answer_images/answer_label_large.png",
                "../answer_images/answer_label.png",
                "../answer_images/answer_label_small.png",
                "../answer_images/answer_label_large.png",
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label.png"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_small.png"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_large.png"),
            ]

            # 找到第一个存在的答案图片
            answer_image_path = None
            for path in answer_image_paths:
                if os.path.exists(path):
                    answer_image_path = path
                    break

            if not answer_image_path:
                print("未找到答案标签图片")
                return None

            # 使用PIL检查图片尺寸
            with PILImage.open(answer_image_path) as pil_img:
                orig_width, orig_height = pil_img.size

                # 计算合适的尺寸（答案图片应该比较小，缩小50%）
                target_width = min(40, max_width * 0.1)  # 最大40像素或10%宽度（缩小50%）
                scale_ratio = min(target_width / orig_width, 1.0)  # 不放大

                new_width = orig_width * scale_ratio
                new_height = orig_height * scale_ratio

                # 创建ReportLab Image对象
                img = Image(answer_image_path, width=new_width, height=new_height)
                return img

        except Exception as e:
            print(f"创建答案图片失败: {e}")
            return None

    def _process_answer_text_replacement(self, text: str) -> str:
        """处理"答案"文字替换为特殊样式"""
        if '答案' not in text:
            return text

        # 查找答案图片路径（考虑不同的工作目录）
        answer_image_paths = [
            "answer_images/answer_label.png",
            "answer_images/answer_label_small.png",
            "answer_images/answer_label_large.png",
            "../answer_images/answer_label.png",
            "../answer_images/answer_label_small.png",
            "../answer_images/answer_label_large.png",
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label.png"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_small.png"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_large.png"),
        ]

        # 找到第一个存在的答案图片
        answer_image_path = None
        for path in answer_image_paths:
            if os.path.exists(path):
                answer_image_path = os.path.abspath(path)
                break

        if answer_image_path:
            # 使用橙色粗体方括号标记替换"答案"
            # 这样既醒目又与原有的双括号样式保持一致
            styled_answer = '<font color="#FF8C00"><b>【答案】</b></font>'
            text = text.replace('答案', styled_answer)
        else:
            # 如果没有图片，使用简单的视觉标记
            text = text.replace('答案', '【答案】')

        return text

    def _parse_image_size(self, img_src_full: str) -> tuple[str, dict]:
        """解析图片路径和尺寸参数

        支持的格式：
        - image.png (默认)
        - image.png?size=small (预设尺寸)
        - image.png?size=medium
        - image.png?size=large
        - image.png?size=original
        - image.png?width=200 (指定宽度)
        - image.png?height=150 (指定高度)
        - image.png?width=200&height=150 (指定宽高)
        """
        if '?' not in img_src_full:
            return img_src_full, {}

        img_src, params_str = img_src_full.split('?', 1)
        size_params = {}

        # 解析参数
        for param in params_str.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                size_params[key.strip()] = value.strip()

        return img_src, size_params

    def _process_image_for_answer_box(self, img_src: str, alt_text: str, max_width: float, size_params: dict = None) -> Optional[Image]:
        """处理答案框中的图片"""
        try:
            image_path = None

            # 判断是网络图片还是本地图片
            if img_src.startswith(('http://', 'https://')):
                # 网络图片 - 尝试从缓存获取
                parsed_url = urlparse(img_src)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    filename = f"image_{uuid.uuid4().hex[:8]}.jpg"

                cache_path = os.path.join("image_cache", filename)

                if os.path.exists(cache_path):
                    image_path = cache_path
                else:
                    print(f"网络图片未缓存，跳过: {img_src}")
                    return None
            else:
                # 本地图片
                possible_paths = [
                    img_src,  # 原始路径
                    os.path.join("test_images", os.path.basename(img_src)),
                    os.path.join("uploads", img_src),
                    os.path.join("uploads", os.path.basename(img_src)),
                    os.path.join("..", img_src),
                    os.path.join("..", "backend", img_src),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        image_path = path
                        break

                if not image_path:
                    print(f"本地图片文件未找到: {img_src}")
                    return None

            # 处理图片
            if image_path:
                # 使用PIL检查图片
                with PILImage.open(image_path) as pil_img:
                    # 获取原始尺寸
                    orig_width, orig_height = pil_img.size

                    # 根据尺寸参数计算新尺寸
                    new_width, new_height = self._calculate_image_size(
                        orig_width, orig_height, max_width, size_params or {}
                    )

                    # 创建ReportLab Image对象
                    img = Image(image_path, width=new_width, height=new_height)
                    return img

            return None

        except Exception as e:
            print(f"处理答案框图片失败 {img_src}: {e}")
            return None

    def _calculate_image_size(self, orig_width: int, orig_height: int, max_width: float, size_params: dict) -> tuple[float, float]:
        """根据尺寸参数计算图片的新尺寸"""

        # 默认最大高度（答案框中的图片）
        default_max_height = 150  # 减小默认高度

        # 如果有具体的宽度和高度参数
        if 'width' in size_params and 'height' in size_params:
            try:
                new_width = float(size_params['width'])
                new_height = float(size_params['height'])
                return new_width, new_height
            except ValueError:
                pass

        # 如果只有宽度参数
        if 'width' in size_params:
            try:
                new_width = float(size_params['width'])
                # 按比例计算高度
                aspect_ratio = orig_height / orig_width
                new_height = new_width * aspect_ratio
                return new_width, new_height
            except ValueError:
                pass

        # 如果只有高度参数
        if 'height' in size_params:
            try:
                new_height = float(size_params['height'])
                # 按比例计算宽度
                aspect_ratio = orig_width / orig_height
                new_width = new_height * aspect_ratio
                return new_width, new_height
            except ValueError:
                pass

        # 预设尺寸选项
        size_option = size_params.get('size', 'auto')

        if size_option == 'original':
            # 原始尺寸，但不超过最大宽度
            if orig_width <= max_width:
                return orig_width, orig_height
            else:
                # 按比例缩放到最大宽度
                scale_ratio = max_width / orig_width
                return orig_width * scale_ratio, orig_height * scale_ratio

        elif size_option == 'small':
            # 小尺寸：最大宽度的40%
            target_width = max_width * 0.4
            scale_ratio = min(target_width / orig_width, 80 / orig_height, 1.0)
            return orig_width * scale_ratio, orig_height * scale_ratio

        elif size_option == 'medium':
            # 中等尺寸：最大宽度的70%
            target_width = max_width * 0.7
            scale_ratio = min(target_width / orig_width, 120 / orig_height, 1.0)
            return orig_width * scale_ratio, orig_height * scale_ratio

        elif size_option == 'large':
            # 大尺寸：最大宽度的90%
            target_width = max_width * 0.9
            scale_ratio = min(target_width / orig_width, 180 / orig_height, 1.0)
            return orig_width * scale_ratio, orig_height * scale_ratio

        else:
            # 默认自动尺寸
            width_ratio = max_width / orig_width
            height_ratio = default_max_height / orig_height
            scale_ratio = min(width_ratio, height_ratio, 1.0)  # 不放大图片
            return orig_width * scale_ratio, orig_height * scale_ratio

    def draw(self):
        """绘制答案及解析框"""
        canvas = self.canv

        # 保存画布状态
        canvas.saveState()

        try:
            # 绘制圆角矩形背景
            canvas.setFillColor(self.bg_color)
            canvas.setStrokeColor(self.border_color)
            canvas.setLineWidth(self.border_width)

            # 绘制带边框的圆角矩形
            canvas.roundRect(
                0, 0,  # 左下角坐标
                self.width, self.height,  # 宽度和高度
                radius=self.corner_radius,
                fill=1, stroke=1
            )

            # 绘制混合内容（文本和图片）
            if hasattr(self, 'content_objects') and self.content_objects:
                # 计算绘制起始位置（考虑内边距和边框）
                content_x = self.padding + self.border_width
                current_y = self.height - self.padding - self.border_width  # 从顶部开始
                content_width = self.width - (self.padding * 2) - (self.border_width * 2)

                # 逐个绘制内容对象
                for content_type, content_obj in self.content_objects:
                    if content_type == 'paragraph':
                        # 绘制段落
                        para_width, para_height = content_obj.wrap(content_width, self.height)
                        current_y -= para_height
                        content_obj.drawOn(canvas, content_x, current_y)
                        current_y -= (self.style.spaceAfter or 6)

                    elif content_type == 'image':
                        # 绘制图片
                        img_height = getattr(content_obj, 'drawHeight', getattr(content_obj, '_height', 100))
                        img_width = getattr(content_obj, 'drawWidth', getattr(content_obj, '_width', 100))
                        current_y -= img_height

                        # 答案图片左对齐显示（突出到答案框外面）
                        img_x = -4  # 负值让图片突出到答案框左边外面10px
                        content_obj.drawOn(canvas, img_x, current_y)
                        current_y -= 5  # 答案图片间距（减少间距）

            # 兼容旧版本：如果没有content_objects，使用paragraph_objects
            elif hasattr(self, 'paragraph_objects') and self.paragraph_objects:
                # 计算文本绘制起始位置（考虑内边距和边框）
                text_x = self.padding + self.border_width
                current_y = self.height - self.padding - self.border_width  # 从顶部开始

                # 逐个绘制段落
                for para in self.paragraph_objects:
                    # 获取段落高度
                    para_width, para_height = para.wrap(
                        self.width - (self.padding * 2) - (self.border_width * 2),
                        self.height
                    )

                    # 调整Y坐标到段落底部
                    current_y -= para_height

                    # 绘制段落
                    para.drawOn(canvas, text_x, current_y)

                    # 添加段落间距
                    current_y -= (self.style.spaceAfter or 6)

        except Exception as e:
            print(f"绘制答案及解析框失败: {e}")
            # 降级到普通文字
            canvas.setFillColor(colors.black)
            canvas.setFont(self.style.fontName, self.style.fontSize)
            # 简单的文本绘制
            lines = self.text.split('\n')
            y_offset = self.height - self.padding - self.style.fontSize
            for line in lines:
                if line.strip():  # 跳过空行
                    canvas.drawString(self.padding, y_offset, line.strip())
                    y_offset -= self.style.fontSize * 1.2

        # 恢复画布状态
        canvas.restoreState()


class ColorBandPageTemplate(PageTemplate):
    """带有顶部和底部色条的页面模板，包含页眉和页码"""

    def __init__(self, id, frames, pagesize, **kwargs):
        super().__init__(id, frames, pagesize=pagesize, **kwargs)
        self.pagesize = pagesize

    def beforeDrawPage(self, canvas, doc):
        """在绘制页面内容之前绘制色条、页眉和页码"""
        # 获取页面尺寸
        page_width, page_height = self.pagesize

        # 顶部色条高度（1.8cm）和底部色条高度（0.5cm）
        top_band_height = 1.8 * cm
        bottom_band_height = 0.5 * cm

        # 色条颜色 #ffe9a9
        band_color = colors.Color(1.0, 0.914, 0.663)  # RGB值转换为0-1范围

        # 保存当前画布状态
        canvas.saveState()

        # 绘制顶部色条（高度为1.0cm）
        canvas.setFillColor(band_color)
        canvas.rect(0, page_height - top_band_height, page_width, top_band_height, fill=1, stroke=0)

        # 绘制底部色条（高度为0.5cm）
        canvas.rect(0, 0, page_width, bottom_band_height, fill=1, stroke=0)

        # 获取当前页码
        page_num = canvas.getPageNumber()

        # 页眉文字
        header_text = "非学而思课堂材料，学员自由领取。"

        # 设置文字颜色为黑色
        canvas.setFillColor(colors.black)

        # 设置字体和大小（使用中文字体）
        try:
            # 尝试使用已注册的中文字体
            canvas.setFont("ChineseFont", 10)
        except:
            # 如果中文字体不可用，使用默认字体
            canvas.setFont("Helvetica", 10)

        # 确定使用的字体名称
        try:
            font_name = "ChineseFont"
            # 测试字体是否可用
            canvas.stringWidth("测试", font_name, 10)
        except:
            font_name = "Helvetica"

        # 判断奇偶页
        is_odd_page = page_num % 2 == 1

        # 页码圆形设计参数
        circle_diameter = 12  # 12mm直径
        circle_radius = circle_diameter / 2
        circle_color = colors.Color(0.969, 0.671, 0.0)  # #f7ab00 转换为RGB (247/255, 171/255, 0/255)

        if is_odd_page:
            # 单数页：页眉文字靠左，页码圆形在左侧
            # 页眉文字在顶部色条中，靠左对齐
            canvas.drawString(20, page_height - top_band_height/2 - 3, header_text)

            # 页码圆形在底部色条上方，左侧位置
            circle_x = 20 + circle_radius  # 圆心X坐标
            circle_y = bottom_band_height + circle_radius + 5  # 圆心Y坐标（底部色条上方5mm）

            # 绘制页码圆形背景
            canvas.setFillColor(circle_color)
            canvas.circle(circle_x, circle_y, circle_radius, fill=1, stroke=0)

            # 绘制页码文字（白色）
            canvas.setFillColor(colors.white)
            canvas.setFont(font_name, 8)  # 稍小的字体适应圆形
            page_text = str(page_num)
            text_width = canvas.stringWidth(page_text, font_name, 8)
            text_x = circle_x - text_width/2  # 文字居中
            text_y = circle_y - 3  # 垂直居中调整
            canvas.drawString(text_x, text_y, page_text)

        else:
            # 双数页：页眉文字靠右，页码圆形在右侧
            # 页眉文字在顶部色条中，靠右对齐
            text_width = canvas.stringWidth(header_text, font_name, 10)
            canvas.drawString(page_width - text_width - 20, page_height - top_band_height/2 - 3, header_text)

            # 页码圆形在底部色条上方，右侧位置
            circle_x = page_width - 20 - circle_radius  # 圆心X坐标
            circle_y = bottom_band_height + circle_radius + 2  # 圆心Y坐标（底部色条上方2mm）

            # 绘制页码圆形背景
            canvas.setFillColor(circle_color)
            canvas.circle(circle_x, circle_y, circle_radius, fill=1, stroke=0)

            # 绘制页码文字（白色）
            canvas.setFillColor(colors.white)
            canvas.setFont(font_name, 8)  # 稍小的字体适应圆形
            page_text = str(page_num)
            text_width = canvas.stringWidth(page_text, font_name, 8)
            text_x = circle_x - text_width/2  # 文字居中
            text_y = circle_y - 3  # 垂直居中调整
            canvas.drawString(text_x, text_y, page_text)

        # 恢复画布状态
        canvas.restoreState()


class PDFService:
    """PDF生成服务类"""

    def __init__(self):
        self.output_dir = "generated_pdfs"
        self.image_cache_dir = "image_cache"
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_cache_dir, exist_ok=True)

        # 页面尺寸映射
        self.page_sizes = {
            'A4': A4,
            'A3': A3,
            'Letter': letter,
            'Legal': legal
        }

        # 注册中文字体
        self._register_chinese_fonts()

    def _register_chinese_fonts(self):
        """注册中文字体"""
        try:
            # 尝试注册系统中文字体
            system = platform.system()

            if system == "Darwin":  # macOS
                # macOS 系统字体路径 - 优先选择支持中文的字体
                font_paths = [
                    "/Library/Fonts/Arial Unicode MS.ttf",  # 最佳选择，支持中文
                    "/System/Library/Fonts/STHeiti Light.ttc",  # 华文黑体
                    "/System/Library/Fonts/Helvetica.ttc",  # 备选
                    "/System/Library/Fonts/Arial.ttf",
                ]
                # 楷体字体路径
                kaiti_paths = [
                    "/System/Library/Fonts/STKaiti.ttc",  # 华文楷体
                    "/Library/Fonts/Kaiti.ttc",  # 楷体
                    "/System/Library/Fonts/Supplemental/Kaiti.ttc",  # 补充楷体
                ]
            elif system == "Windows":
                # Windows 系统字体路径
                font_paths = [
                    "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                    "C:/Windows/Fonts/simsun.ttc",  # 宋体
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/arial.ttf",  # Arial
                ]
                # 楷体字体路径
                kaiti_paths = [
                    "C:/Windows/Fonts/simkai.ttf",  # 楷体
                    "C:/Windows/Fonts/STKAITI.TTF",  # 华文楷体
                ]
            else:  # Linux
                font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",
                ]
                # 楷体字体路径
                kaiti_paths = [
                    "/usr/share/fonts/truetype/arphic/ukai.ttc",  # 文鼎楷体
                    "/usr/share/fonts/truetype/noto/NotoSerifCJK-Regular.ttc",  # Noto楷体
                ]

            # 尝试注册第一个可用的字体
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        # 跳过TTC文件中的PostScript字体
                        if font_path.endswith('.ttc') and 'PingFang' in font_path:
                            continue

                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        print(f"成功注册中文字体: {font_path}")

                        # 测试字体是否真正支持中文
                        if self._test_chinese_support():
                            break
                        else:
                            print(f"字体 {font_path} 不支持中文，继续尝试下一个")

                    except Exception as e:
                        print(f"注册字体失败 {font_path}: {e}")
                        continue

            # 尝试注册楷体字体
            kaiti_registered = False
            for kaiti_path in kaiti_paths:
                if os.path.exists(kaiti_path):
                    try:
                        pdfmetrics.registerFont(TTFont('KaiTi', kaiti_path))
                        print(f"成功注册楷体字体: {kaiti_path}")
                        kaiti_registered = True
                        break
                    except Exception as e:
                        print(f"注册楷体字体失败 {kaiti_path}: {e}")
                        continue

            if not kaiti_registered:
                print("未找到系统楷体字体，将使用中文字体作为楷体备选")

            # 如果没有找到合适的字体，使用ReportLab的内置Unicode支持
            print("未找到合适的系统中文字体，使用内置Unicode字体")
            self._register_builtin_unicode_font()

        except Exception as e:
            print(f"字体注册过程出错: {e}")

    def _test_chinese_support(self) -> bool:
        """测试当前注册的字体是否支持中文"""
        try:
            # 简单测试：尝试创建包含中文的段落
            from reportlab.platypus import Paragraph
            from reportlab.lib.styles import ParagraphStyle

            test_style = ParagraphStyle('Test', fontName='ChineseFont', fontSize=12)
            test_para = Paragraph("测试中文", test_style)
            return True
        except:
            return False

    def _register_builtin_unicode_font(self):
        """注册内置的Unicode字体"""
        try:
            # 使用ReportLab内置的Unicode CID字体
            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
            pdfmetrics.registerFont(UnicodeCIDFont('STSongStd-Light'))
            print("注册内置Unicode CID字体成功")

            # 尝试注册楷体的CID字体
            try:
                pdfmetrics.registerFont(UnicodeCIDFont('STKaiti'))
                print("注册内置楷体CID字体成功")
            except:
                # 如果楷体CID字体不可用，使用宋体作为楷体的备选
                print("楷体CID字体不可用，将使用宋体作为楷体备选")

        except Exception as e:
            print(f"注册内置Unicode字体失败: {e}")
            # 最后的备选方案：使用Helvetica
            print("使用Helvetica作为备选字体")

    def _get_available_font(self) -> str:
        """获取可用的字体名称"""
        # 检查是否已注册中文字体
        registered_fonts = pdfmetrics.getRegisteredFontNames()

        # 优先级顺序：自定义中文字体 > CID字体 > 默认字体
        if 'ChineseFont' in registered_fonts:
            return 'ChineseFont'
        elif 'STSong-Light' in registered_fonts:
            return 'STSong-Light'
        elif 'STSongStd-Light' in registered_fonts:
            return 'STSongStd-Light'

        # 如果没有中文字体，返回默认字体
        return 'Helvetica'

    def _get_available_kaiti_font(self) -> str:
        """获取可用的楷体字体名称"""
        # 检查是否已注册楷体字体
        registered_fonts = pdfmetrics.getRegisteredFontNames()

        # 优先级顺序：自定义楷体字体 > CID楷体字体 > 中文字体 > 默认字体
        if 'KaiTi' in registered_fonts:
            return 'KaiTi'
        elif 'STKaiti' in registered_fonts:
            return 'STKaiti'
        else:
            # 如果没有楷体字体，使用普通中文字体
            return self._get_available_font()

    async def generate_pdf(
        self,
        content: str,
        config: LayoutConfig,
        filename: Optional[str] = None
    ) -> str:
        """
        生成PDF文件
        """

        # 预处理：下载网络图片
        await self._preprocess_images(content)

        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}.pdf"
        elif not filename.endswith('.pdf'):
            filename += '.pdf'

        pdf_path = os.path.join(self.output_dir, filename)

        # 在线程池中生成PDF以避免阻塞
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._generate_pdf_sync, content, config, pdf_path)

        return pdf_path

    async def _preprocess_images(self, content: str):
        """预处理内容中的图片，下载网络图片到缓存"""
        # 查找所有图片引用
        img_pattern = r'!\[(.*?)\]\((.*?)\)'
        matches = re.findall(img_pattern, content)

        # 下载所有网络图片
        for alt_text, img_src in matches:
            if img_src.startswith(('http://', 'https://')):
                await self._download_image(img_src)

    def _generate_pdf_sync(self, content: str, config: LayoutConfig, output_path: str):
        """同步生成PDF"""

        # 获取页面尺寸
        page_size = self.page_sizes.get(config.page_format, A4)
        page_width, page_height = page_size

        # 色条高度
        top_band_height = 1.0 * cm  # 顶部色条高度
        bottom_band_height = 0.5 * cm  # 底部色条高度

        # 页码圆形需要的额外空间（8mm直径 + 2mm间距）
        page_number_space = 10  # 10mm总空间

        # 调整边距以适应色条和页码圆形
        top_margin = config.margin_top * cm + top_band_height
        bottom_margin = config.margin_bottom * cm + bottom_band_height + page_number_space
        left_margin = config.margin_left * cm
        right_margin = config.margin_right * cm

        # 创建基础文档模板
        doc = BaseDocTemplate(
            output_path,
            pagesize=page_size,
            topMargin=top_margin,
            bottomMargin=bottom_margin,
            leftMargin=left_margin,
            rightMargin=right_margin
        )

        # 创建框架（内容区域）
        frame = Frame(
            left_margin,
            bottom_margin,
            page_width - left_margin - right_margin,
            page_height - top_margin - bottom_margin,
            id='normal'
        )

        # 创建带色条的页面模板
        template = ColorBandPageTemplate(
            id='main',
            frames=[frame],
            pagesize=page_size
        )

        # 添加页面模板到文档
        doc.addPageTemplates([template])

        # 创建样式
        styles = self._create_styles(config)

        # 解析Markdown并转换为PDF元素
        story = self._markdown_to_pdf_elements(content, styles)

        # 构建PDF
        doc.build(story)
    
    def _create_styles(self, config: LayoutConfig) -> Dict[str, ParagraphStyle]:
        """创建PDF样式"""

        base_styles = getSampleStyleSheet()

        # 确定使用的字体
        font_name = self._get_available_font()

        # 基础段落样式
        normal_style = ParagraphStyle(
            'Normal',
            parent=base_styles['Normal'],
            fontName=font_name,
            fontSize=config.font_size,
            leading=config.font_size * config.line_height,
            spaceAfter=config.paragraph_spacing,
            alignment=TA_JUSTIFY,
            firstLineIndent=20 if config.indent_first_line else 0
        )

        # 标题样式 - 一级标题应用背景图片样式
        heading1_style = ParagraphStyle(
            'Heading1',
            parent=normal_style,
            fontSize=config.font_size * 2.5,  # 更大的字体适应背景图片
            spaceAfter=config.paragraph_spacing * 2,
            spaceBefore=config.paragraph_spacing * 2,
            alignment=TA_CENTER,  # 居中对齐
            fontName=font_name,
            textColor=colors.white,  # 白色文字在橙色背景上
            backColor=colors.Color(0.95, 0.95, 0.95),  # 备用背景色
            borderColor=colors.Color(0.8, 0.8, 0.8),  # 备用边框颜色
            borderWidth=1,  # 边框宽度
            borderPadding=15,  # 内边距
            leftIndent=0,  # 不需要缩进，因为使用背景图片
            rightIndent=0,  # 不需要缩进
        )

        heading2_style = ParagraphStyle(
            'Heading2',
            parent=normal_style,
            fontSize=config.font_size * 1.5,
            spaceAfter=config.paragraph_spacing * 1.5,
            spaceBefore=config.paragraph_spacing * 1.5,
            fontName=font_name
        )

        heading3_style = ParagraphStyle(
            'Heading3',
            parent=normal_style,
            fontSize=config.font_size * 1.3,
            spaceAfter=config.paragraph_spacing,
            spaceBefore=config.paragraph_spacing,
            fontName=font_name
        )

        # 代码样式
        code_style = ParagraphStyle(
            'Code',
            parent=normal_style,
            fontName='Courier',
            fontSize=config.font_size * 0.9,
            backColor=colors.lightgrey,
            borderColor=colors.grey,
            borderWidth=1,
            borderPadding=5
        )

        return {
            'normal': normal_style,
            'heading1': heading1_style,
            'heading2': heading2_style,
            'heading3': heading3_style,
            'code': code_style
        }
    
    def _markdown_to_pdf_elements(self, content: str, styles: Dict[str, ParagraphStyle]) -> List:
        """将Markdown内容转换为PDF元素"""

        story = []
        lines = content.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if not line:
                # 空行
                story.append(Spacer(1, 6))
                i += 1
                continue

            # 图片处理 - 检查是否是图片语法 ![alt](src)
            img_match = re.match(r'!\[(.*?)\]\((.*?)\)', line)
            if img_match:
                alt_text = img_match.group(1)
                img_src = img_match.group(2)

                # 处理图片
                img_element = self._process_image_sync(img_src, alt_text)
                if img_element:
                    story.append(img_element)
                    # 如果有alt文本，添加图片说明
                    if alt_text:
                        caption_style = ParagraphStyle(
                            'ImageCaption',
                            parent=styles['normal'],
                            fontSize=styles['normal'].fontSize * 0.9,
                            alignment=TA_CENTER,
                            spaceAfter=12,
                            textColor=colors.grey
                        )
                        story.append(Paragraph(f"<i>{alt_text}</i>", caption_style))
                else:
                    # 如果图片处理失败，显示alt文本
                    if alt_text:
                        story.append(Paragraph(f"[图片: {alt_text}]", styles['normal']))
                i += 1
                continue

            # 标题
            if line.startswith('# '):
                text = line[2:].strip()
                # 使用带背景图片的一级标题
                background_image_path = os.path.join('backend', 'assets', 'heading_background.png')
                if not os.path.exists(background_image_path):
                    # 如果背景图片不存在，尝试相对路径
                    background_image_path = os.path.join('assets', 'heading_background.png')

                heading_element = ImageBackgroundHeading(
                    text,
                    styles['heading1'],
                    background_image_path if os.path.exists(background_image_path) else None
                )
                story.append(heading_element)
                # 添加60px间距
                story.append(Spacer(1, 60))
            elif line.startswith('## '):
                text = line[3:].strip()
                story.append(Paragraph(text, styles['heading2']))
            elif line.startswith('### '):
                text = line[4:].strip()
                story.append(Paragraph(text, styles['heading3']))

            # 编号列表处理
            elif re.match(r'^\d+\.\s+', line):
                # 匹配编号列表项（如 "1. 内容"）
                match = re.match(r'^(\d+)\.\s+(.*)', line)
                if match:
                    number = int(match.group(1))
                    text = match.group(2).strip()

                    # 处理行内Markdown格式
                    text = self._process_inline_markdown(text)

                    # 创建带背景图片的编号列表项
                    background_image_path = os.path.join('backend', 'assets', 'numbered_list_background.png')
                    if not os.path.exists(background_image_path):
                        # 如果背景图片不存在，尝试相对路径
                        background_image_path = os.path.join('assets', 'numbered_list_background.png')

                    list_item = NumberedListItem(
                        number,
                        text,
                        styles['normal'],
                        background_image_path if os.path.exists(background_image_path) else None
                    )
                    story.append(list_item)
                    # 添加小间距
                    story.append(Spacer(1, 5))

            # 答案及解析框处理 - 支持多段落内容
            elif line.strip().startswith('/'):
                # 收集斜杠包围的多段落内容
                content_lines = []
                current_line = line.strip()

                # 检查是否是单行格式 /内容/
                if current_line.endswith('/') and len(current_line) > 2:
                    # 单行格式
                    text = current_line[1:-1].strip()
                    content_lines.append(text)
                else:
                    # 多行格式，收集到结束斜杠
                    if current_line.startswith('/'):
                        content_lines.append(current_line[1:])  # 去掉开始的斜杠

                    i += 1
                    while i < len(lines):
                        current_line = lines[i].strip()
                        if current_line.endswith('/'):
                            # 找到结束斜杠
                            content_lines.append(current_line[:-1])  # 去掉结束的斜杠
                            break
                        else:
                            content_lines.append(current_line)
                        i += 1

                # 处理收集到的内容
                if content_lines:
                    # 将多行内容合并，保留段落分隔
                    full_text = '\n'.join(content_lines).strip()

                    # 创建答案及解析框样式
                    answer_style = ParagraphStyle(
                        'AnswerAnalysis',
                        parent=styles['normal'],
                        fontName=self._get_available_kaiti_font(),  # 使用楷体
                        fontSize=styles['normal'].fontSize,
                        leading=styles['normal'].fontSize * 1.3,
                        alignment=TA_JUSTIFY,
                        leftIndent=0,
                        rightIndent=0,
                        spaceAfter=6  # 段落间距
                    )

                    # 创建答案及解析框
                    answer_box = AnswerAnalysisBox(full_text, answer_style)
                    story.append(answer_box)
                    # 添加间距
                    story.append(Spacer(1, 10))

            # 代码块
            elif line.startswith('```'):
                i += 1
                code_lines = []
                while i < len(lines) and not lines[i].strip().startswith('```'):
                    code_lines.append(lines[i])
                    i += 1
                code_text = '\n'.join(code_lines)
                story.append(Paragraph(f'<pre>{code_text}</pre>', styles['code']))

            # 普通段落
            else:
                # 收集连续的非空行作为一个段落
                paragraph_lines = [line]
                i += 1
                while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('#') and not re.match(r'!\[(.*?)\]\((.*?)\)', lines[i].strip()) and not re.match(r'^\d+\.\s+', lines[i].strip()) and not re.match(r'^/.*/$', lines[i].strip()):
                    paragraph_lines.append(lines[i].strip())
                    i += 1

                paragraph_text = ' '.join(paragraph_lines)
                # 处理简单的Markdown格式
                paragraph_text = self._process_inline_markdown(paragraph_text)
                story.append(Paragraph(paragraph_text, styles['normal']))
                continue

            i += 1

        return story

    def _process_inline_markdown(self, text: str) -> str:
        """处理行内Markdown格式"""

        # 双括号文本 - 橘色楷体文字，保留一个括号
        # 获取可用的楷体字体名称
        kaiti_font = self._get_available_kaiti_font()
        text = re.sub(r'（（(.*?)））', rf'<font color="#FF8C00" name="{kaiti_font}">（\1）</font>', text)

        # 粗体
        text = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', text)
        text = re.sub(r'__(.*?)__', r'<b>\1</b>', text)

        # 斜体
        text = re.sub(r'\*(.*?)\*', r'<i>\1</i>', text)
        text = re.sub(r'_(.*?)_', r'<i>\1</i>', text)

        # 行内代码
        text = re.sub(r'`(.*?)`', r'<font name="Courier">\1</font>', text)

        return text

    def _process_image_sync(self, img_src: str, alt_text: str = "") -> Optional[Image]:
        """同步处理图片（用于PDF生成）"""
        try:
            image_path = None

            # 判断是网络图片还是本地图片
            if img_src.startswith(('http://', 'https://')):
                # 网络图片 - 尝试从缓存获取
                parsed_url = urlparse(img_src)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    filename = f"image_{uuid.uuid4().hex[:8]}.jpg"

                cache_path = os.path.join(self.image_cache_dir, filename)

                if os.path.exists(cache_path):
                    image_path = cache_path
                else:
                    # 网络图片但未缓存，跳过（在实际应用中可以考虑同步下载）
                    print(f"网络图片未缓存，跳过: {img_src}")
                    return None
            else:
                # 本地图片
                # 尝试相对于不同目录的路径
                possible_paths = [
                    img_src,  # 原始路径
                    os.path.join("test_images", os.path.basename(img_src)),  # test_images目录（相对于backend）
                    os.path.join("uploads", img_src),  # uploads目录
                    os.path.join("uploads", os.path.basename(img_src)),  # uploads目录中的文件名
                    os.path.join("..", img_src),  # 相对于上级目录
                    os.path.join("..", "backend", img_src),  # 相对于项目根目录的backend
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        image_path = path
                        break

                if not image_path:
                    print(f"本地图片文件未找到: {img_src}")
                    return None

            # 处理图片
            if image_path:
                return self._process_image_for_pdf(image_path)

            return None

        except Exception as e:
            print(f"处理图片失败 {img_src}: {e}")
            return None

    async def _download_image(self, url: str) -> Optional[str]:
        """下载网络图片并缓存到本地"""
        try:
            # 解析URL获取文件名
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                # 如果没有文件名或扩展名，生成一个
                filename = f"image_{uuid.uuid4().hex[:8]}.jpg"

            # 缓存文件路径
            cache_path = os.path.join(self.image_cache_dir, filename)

            # 如果已经缓存，直接返回
            if os.path.exists(cache_path):
                return cache_path

            # 下载图片
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.read()
                        async with aiofiles.open(cache_path, 'wb') as f:
                            await f.write(content)
                        return cache_path

            return None
        except Exception as e:
            print(f"下载图片失败 {url}: {e}")
            return None

    def _process_image_for_pdf(self, image_path: str, max_width: float = 400, max_height: float = 300) -> Optional[Image]:
        """处理图片用于PDF插入"""
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return None

            # 使用PIL检查图片
            with PILImage.open(image_path) as pil_img:
                # 获取原始尺寸
                orig_width, orig_height = pil_img.size

                # 计算缩放比例
                width_ratio = max_width / orig_width
                height_ratio = max_height / orig_height
                scale_ratio = min(width_ratio, height_ratio, 1.0)  # 不放大图片

                # 计算新尺寸
                new_width = orig_width * scale_ratio
                new_height = orig_height * scale_ratio

                # 创建ReportLab Image对象
                img = Image(image_path, width=new_width, height=new_height)
                return img

        except Exception as e:
            print(f"处理图片失败 {image_path}: {e}")
            return None

    async def generate_pdf_preview(self, content: str, config: LayoutConfig) -> str:
        """生成PDF预览（返回base64编码）"""

        # 预处理：下载网络图片
        await self._preprocess_images(content)

        # 生成临时PDF
        temp_filename = f"preview_{uuid.uuid4().hex[:8]}.pdf"
        temp_path = os.path.join(self.output_dir, temp_filename)

        try:
            # 生成PDF
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._generate_pdf_sync, content, config, temp_path)

            # 读取PDF并转换为base64
            with open(temp_path, 'rb') as f:
                pdf_data = f.read()

            return base64.b64encode(pdf_data).decode('utf-8')

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
    
    async def get_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            from PyPDF2 import PdfReader
            with open(pdf_path, 'rb') as f:
                pdf_reader = PdfReader(f)
                return len(pdf_reader.pages)
        except Exception:
            # 如果PyPDF2不可用，简单估算
            try:
                file_size = os.path.getsize(pdf_path)
                # 估算：每页约5KB
                return max(1, file_size // 5000)
            except:
                return 1
    
    def get_pdf_path(self, filename: str) -> str:
        """获取PDF文件路径"""
        return os.path.join(self.output_dir, filename)
    
    def list_generated_pdfs(self) -> List[Dict[str, Any]]:
        """列出已生成的PDF文件"""
        pdfs = []

        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.pdf'):
                    file_path = os.path.join(self.output_dir, filename)
                    if os.path.isfile(file_path):
                        file_stats = os.stat(file_path)

                        pdfs.append({
                            "filename": filename,
                            "size": file_stats.st_size,
                            "created_at": file_stats.st_ctime,
                            "download_url": f"/api/pdf/download/{filename}"
                        })

        # 按创建时间排序
        pdfs.sort(key=lambda x: x['created_at'], reverse=True)
        return pdfs
    
    def delete_pdf(self, filename: str) -> bool:
        """删除PDF文件"""
        try:
            file_path = os.path.join(self.output_dir, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
