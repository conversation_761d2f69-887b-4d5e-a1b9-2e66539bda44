# -*- coding: utf-8 -*-
#
# NOTE: This file was auto-generated with MetaTools/buildUCD.py.
# Source: https://unicode.org/Public/UNIDATA/ScriptExtensions.txt
# License: http://unicode.org/copyright.html#License
#
# ScriptExtensions-15.0.0.txt
# Date: 2022-02-02, 00:57:11 GMT
# © 2022 Unicode®, Inc.
# Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
# For terms of use, see https://www.unicode.org/terms_of_use.html
#
# Unicode Character Database
#   For documentation, see https://www.unicode.org/reports/tr44/
#
# The Script_Extensions property indicates which characters are commonly used
# with more than one script, but with a limited number of scripts.
# For each code point, there is one or more property values.  Each such value is a Script property value.
# For more information, see:
#   UAX #24, Unicode Script Property: https://www.unicode.org/reports/tr24/
#     Especially the sections:
#       https://www.unicode.org/reports/tr24/#Assignment_Script_Values
#       https://www.unicode.org/reports/tr24/#Assignment_ScriptX_Values
#
# Each Script_Extensions value in this file consists of a set
# of one or more abbreviated Script property values. The ordering of the
# values in that set is not material, but for stability in presentation
# it is given here as alphabetical.
#
# The Script_Extensions values are presented in sorted order in the file.
# They are sorted first by the number of Script property values in their sets,
# and then alphabetically by first differing Script property value.
#
# Following each distinct Script_Extensions value is the list of code
# points associated with that value, listed in code point order.
#
# All code points not explicitly listed for Script_Extensions
# have as their value the corresponding Script property value
#
# @missing: 0000..10FFFF; <script>


RANGES = [
    0x0000,  # .. 0x0341 ; None
    0x0342,  # .. 0x0342 ; {'Grek'}
    0x0343,  # .. 0x0344 ; None
    0x0345,  # .. 0x0345 ; {'Grek'}
    0x0346,  # .. 0x0362 ; None
    0x0363,  # .. 0x036F ; {'Latn'}
    0x0370,  # .. 0x0482 ; None
    0x0483,  # .. 0x0483 ; {'Cyrl', 'Perm'}
    0x0484,  # .. 0x0484 ; {'Cyrl', 'Glag'}
    0x0485,  # .. 0x0486 ; {'Cyrl', 'Latn'}
    0x0487,  # .. 0x0487 ; {'Cyrl', 'Glag'}
    0x0488,  # .. 0x060B ; None
    0x060C,  # .. 0x060C ; {'Arab', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x060D,  # .. 0x061A ; None
    0x061B,  # .. 0x061B ; {'Arab', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x061C,  # .. 0x061C ; {'Arab', 'Syrc', 'Thaa'}
    0x061D,  # .. 0x061E ; None
    0x061F,  # .. 0x061F ; {'Adlm', 'Arab', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x0620,  # .. 0x063F ; None
    0x0640,  # .. 0x0640 ; {'Adlm', 'Arab', 'Mand', 'Mani', 'Ougr', 'Phlp', 'Rohg', 'Sogd', 'Syrc'}
    0x0641,  # .. 0x064A ; None
    0x064B,  # .. 0x0655 ; {'Arab', 'Syrc'}
    0x0656,  # .. 0x065F ; None
    0x0660,  # .. 0x0669 ; {'Arab', 'Thaa', 'Yezi'}
    0x066A,  # .. 0x066F ; None
    0x0670,  # .. 0x0670 ; {'Arab', 'Syrc'}
    0x0671,  # .. 0x06D3 ; None
    0x06D4,  # .. 0x06D4 ; {'Arab', 'Rohg'}
    0x06D5,  # .. 0x0950 ; None
    0x0951,  # .. 0x0951 ; {'Beng', 'Deva', 'Gran', 'Gujr', 'Guru', 'Knda', 'Latn', 'Mlym', 'Orya', 'Shrd', 'Taml', 'Telu', 'Tirh'}
    0x0952,  # .. 0x0952 ; {'Beng', 'Deva', 'Gran', 'Gujr', 'Guru', 'Knda', 'Latn', 'Mlym', 'Orya', 'Taml', 'Telu', 'Tirh'}
    0x0953,  # .. 0x0963 ; None
    0x0964,  # .. 0x0964 ; {'Beng', 'Deva', 'Dogr', 'Gong', 'Gonm', 'Gran', 'Gujr', 'Guru', 'Knda', 'Mahj', 'Mlym', 'Nand', 'Orya', 'Sind', 'Sinh', 'Sylo', 'Takr', 'Taml', 'Telu', 'Tirh'}
    0x0965,  # .. 0x0965 ; {'Beng', 'Deva', 'Dogr', 'Gong', 'Gonm', 'Gran', 'Gujr', 'Guru', 'Knda', 'Limb', 'Mahj', 'Mlym', 'Nand', 'Orya', 'Sind', 'Sinh', 'Sylo', 'Takr', 'Taml', 'Telu', 'Tirh'}
    0x0966,  # .. 0x096F ; {'Deva', 'Dogr', 'Kthi', 'Mahj'}
    0x0970,  # .. 0x09E5 ; None
    0x09E6,  # .. 0x09EF ; {'Beng', 'Cakm', 'Sylo'}
    0x09F0,  # .. 0x0A65 ; None
    0x0A66,  # .. 0x0A6F ; {'Guru', 'Mult'}
    0x0A70,  # .. 0x0AE5 ; None
    0x0AE6,  # .. 0x0AEF ; {'Gujr', 'Khoj'}
    0x0AF0,  # .. 0x0BE5 ; None
    0x0BE6,  # .. 0x0BF3 ; {'Gran', 'Taml'}
    0x0BF4,  # .. 0x0CE5 ; None
    0x0CE6,  # .. 0x0CEF ; {'Knda', 'Nand'}
    0x0CF0,  # .. 0x103F ; None
    0x1040,  # .. 0x1049 ; {'Cakm', 'Mymr', 'Tale'}
    0x104A,  # .. 0x10FA ; None
    0x10FB,  # .. 0x10FB ; {'Geor', 'Latn'}
    0x10FC,  # .. 0x1734 ; None
    0x1735,  # .. 0x1736 ; {'Buhd', 'Hano', 'Tagb', 'Tglg'}
    0x1737,  # .. 0x1801 ; None
    0x1802,  # .. 0x1803 ; {'Mong', 'Phag'}
    0x1804,  # .. 0x1804 ; None
    0x1805,  # .. 0x1805 ; {'Mong', 'Phag'}
    0x1806,  # .. 0x1CCF ; None
    0x1CD0,  # .. 0x1CD0 ; {'Beng', 'Deva', 'Gran', 'Knda'}
    0x1CD1,  # .. 0x1CD1 ; {'Deva'}
    0x1CD2,  # .. 0x1CD2 ; {'Beng', 'Deva', 'Gran', 'Knda'}
    0x1CD3,  # .. 0x1CD3 ; {'Deva', 'Gran'}
    0x1CD4,  # .. 0x1CD4 ; {'Deva'}
    0x1CD5,  # .. 0x1CD6 ; {'Beng', 'Deva'}
    0x1CD7,  # .. 0x1CD7 ; {'Deva', 'Shrd'}
    0x1CD8,  # .. 0x1CD8 ; {'Beng', 'Deva'}
    0x1CD9,  # .. 0x1CD9 ; {'Deva', 'Shrd'}
    0x1CDA,  # .. 0x1CDA ; {'Deva', 'Knda', 'Mlym', 'Orya', 'Taml', 'Telu'}
    0x1CDB,  # .. 0x1CDB ; {'Deva'}
    0x1CDC,  # .. 0x1CDD ; {'Deva', 'Shrd'}
    0x1CDE,  # .. 0x1CDF ; {'Deva'}
    0x1CE0,  # .. 0x1CE0 ; {'Deva', 'Shrd'}
    0x1CE1,  # .. 0x1CE1 ; {'Beng', 'Deva'}
    0x1CE2,  # .. 0x1CE8 ; {'Deva'}
    0x1CE9,  # .. 0x1CE9 ; {'Deva', 'Nand'}
    0x1CEA,  # .. 0x1CEA ; {'Beng', 'Deva'}
    0x1CEB,  # .. 0x1CEC ; {'Deva'}
    0x1CED,  # .. 0x1CED ; {'Beng', 'Deva'}
    0x1CEE,  # .. 0x1CF1 ; {'Deva'}
    0x1CF2,  # .. 0x1CF2 ; {'Beng', 'Deva', 'Gran', 'Knda', 'Nand', 'Orya', 'Telu', 'Tirh'}
    0x1CF3,  # .. 0x1CF3 ; {'Deva', 'Gran'}
    0x1CF4,  # .. 0x1CF4 ; {'Deva', 'Gran', 'Knda'}
    0x1CF5,  # .. 0x1CF6 ; {'Beng', 'Deva'}
    0x1CF7,  # .. 0x1CF7 ; {'Beng'}
    0x1CF8,  # .. 0x1CF9 ; {'Deva', 'Gran'}
    0x1CFA,  # .. 0x1CFA ; {'Nand'}
    0x1CFB,  # .. 0x1DBF ; None
    0x1DC0,  # .. 0x1DC1 ; {'Grek'}
    0x1DC2,  # .. 0x1DF7 ; None
    0x1DF8,  # .. 0x1DF8 ; {'Cyrl', 'Syrc'}
    0x1DF9,  # .. 0x1DF9 ; None
    0x1DFA,  # .. 0x1DFA ; {'Syrc'}
    0x1DFB,  # .. 0x202E ; None
    0x202F,  # .. 0x202F ; {'Latn', 'Mong'}
    0x2030,  # .. 0x20EF ; None
    0x20F0,  # .. 0x20F0 ; {'Deva', 'Gran', 'Latn'}
    0x20F1,  # .. 0x2E42 ; None
    0x2E43,  # .. 0x2E43 ; {'Cyrl', 'Glag'}
    0x2E44,  # .. 0x3000 ; None
    0x3001,  # .. 0x3002 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x3003,  # .. 0x3003 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3004,  # .. 0x3005 ; None
    0x3006,  # .. 0x3006 ; {'Hani'}
    0x3007,  # .. 0x3007 ; None
    0x3008,  # .. 0x3011 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x3012,  # .. 0x3012 ; None
    0x3013,  # .. 0x3013 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3014,  # .. 0x301B ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x301C,  # .. 0x301F ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3020,  # .. 0x3029 ; None
    0x302A,  # .. 0x302D ; {'Bopo', 'Hani'}
    0x302E,  # .. 0x302F ; None
    0x3030,  # .. 0x3030 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3031,  # .. 0x3035 ; {'Hira', 'Kana'}
    0x3036,  # .. 0x3036 ; None
    0x3037,  # .. 0x3037 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3038,  # .. 0x303B ; None
    0x303C,  # .. 0x303D ; {'Hani', 'Hira', 'Kana'}
    0x303E,  # .. 0x303F ; {'Hani'}
    0x3040,  # .. 0x3098 ; None
    0x3099,  # .. 0x309C ; {'Hira', 'Kana'}
    0x309D,  # .. 0x309F ; None
    0x30A0,  # .. 0x30A0 ; {'Hira', 'Kana'}
    0x30A1,  # .. 0x30FA ; None
    0x30FB,  # .. 0x30FB ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x30FC,  # .. 0x30FC ; {'Hira', 'Kana'}
    0x30FD,  # .. 0x318F ; None
    0x3190,  # .. 0x319F ; {'Hani'}
    0x31A0,  # .. 0x31BF ; None
    0x31C0,  # .. 0x31E3 ; {'Hani'}
    0x31E4,  # .. 0x321F ; None
    0x3220,  # .. 0x3247 ; {'Hani'}
    0x3248,  # .. 0x327F ; None
    0x3280,  # .. 0x32B0 ; {'Hani'}
    0x32B1,  # .. 0x32BF ; None
    0x32C0,  # .. 0x32CB ; {'Hani'}
    0x32CC,  # .. 0x32FE ; None
    0x32FF,  # .. 0x32FF ; {'Hani'}
    0x3300,  # .. 0x3357 ; None
    0x3358,  # .. 0x3370 ; {'Hani'}
    0x3371,  # .. 0x337A ; None
    0x337B,  # .. 0x337F ; {'Hani'}
    0x3380,  # .. 0x33DF ; None
    0x33E0,  # .. 0x33FE ; {'Hani'}
    0x33FF,  # .. 0xA66E ; None
    0xA66F,  # .. 0xA66F ; {'Cyrl', 'Glag'}
    0xA670,  # .. 0xA6FF ; None
    0xA700,  # .. 0xA707 ; {'Hani', 'Latn'}
    0xA708,  # .. 0xA82F ; None
    0xA830,  # .. 0xA832 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Knda', 'Kthi', 'Mahj', 'Mlym', 'Modi', 'Nand', 'Sind', 'Takr', 'Tirh'}
    0xA833,  # .. 0xA835 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Knda', 'Kthi', 'Mahj', 'Modi', 'Nand', 'Sind', 'Takr', 'Tirh'}
    0xA836,  # .. 0xA839 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Kthi', 'Mahj', 'Modi', 'Sind', 'Takr', 'Tirh'}
    0xA83A,  # .. 0xA8F0 ; None
    0xA8F1,  # .. 0xA8F1 ; {'Beng', 'Deva'}
    0xA8F2,  # .. 0xA8F2 ; None
    0xA8F3,  # .. 0xA8F3 ; {'Deva', 'Taml'}
    0xA8F4,  # .. 0xA92D ; None
    0xA92E,  # .. 0xA92E ; {'Kali', 'Latn', 'Mymr'}
    0xA92F,  # .. 0xA9CE ; None
    0xA9CF,  # .. 0xA9CF ; {'Bugi', 'Java'}
    0xA9D0,  # .. 0xFD3D ; None
    0xFD3E,  # .. 0xFD3F ; {'Arab', 'Nkoo'}
    0xFD40,  # .. 0xFDF1 ; None
    0xFDF2,  # .. 0xFDF2 ; {'Arab', 'Thaa'}
    0xFDF3,  # .. 0xFDFC ; None
    0xFDFD,  # .. 0xFDFD ; {'Arab', 'Thaa'}
    0xFDFE,  # .. 0xFE44 ; None
    0xFE45,  # .. 0xFE46 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0xFE47,  # .. 0xFF60 ; None
    0xFF61,  # .. 0xFF65 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0xFF66,  # .. 0xFF6F ; None
    0xFF70,  # .. 0xFF70 ; {'Hira', 'Kana'}
    0xFF71,  # .. 0xFF9D ; None
    0xFF9E,  # .. 0xFF9F ; {'Hira', 'Kana'}
    0xFFA0,  # .. 0x100FF ; None
    0x10100,  # .. 0x10101 ; {'Cpmn', 'Cprt', 'Linb'}
    0x10102,  # .. 0x10102 ; {'Cprt', 'Linb'}
    0x10103,  # .. 0x10106 ; None
    0x10107,  # .. 0x10133 ; {'Cprt', 'Lina', 'Linb'}
    0x10134,  # .. 0x10136 ; None
    0x10137,  # .. 0x1013F ; {'Cprt', 'Linb'}
    0x10140,  # .. 0x102DF ; None
    0x102E0,  # .. 0x102FB ; {'Arab', 'Copt'}
    0x102FC,  # .. 0x10AF1 ; None
    0x10AF2,  # .. 0x10AF2 ; {'Mani', 'Ougr'}
    0x10AF3,  # .. 0x11300 ; None
    0x11301,  # .. 0x11301 ; {'Gran', 'Taml'}
    0x11302,  # .. 0x11302 ; None
    0x11303,  # .. 0x11303 ; {'Gran', 'Taml'}
    0x11304,  # .. 0x1133A ; None
    0x1133B,  # .. 0x1133C ; {'Gran', 'Taml'}
    0x1133D,  # .. 0x11FCF ; None
    0x11FD0,  # .. 0x11FD1 ; {'Gran', 'Taml'}
    0x11FD2,  # .. 0x11FD2 ; None
    0x11FD3,  # .. 0x11FD3 ; {'Gran', 'Taml'}
    0x11FD4,  # .. 0x1BC9F ; None
    0x1BCA0,  # .. 0x1BCA3 ; {'Dupl'}
    0x1BCA4,  # .. 0x1D35F ; None
    0x1D360,  # .. 0x1D371 ; {'Hani'}
    0x1D372,  # .. 0x1F24F ; None
    0x1F250,  # .. 0x1F251 ; {'Hani'}
    0x1F252,  # .. 0x10FFFF ; None
]

VALUES = [
    None,  # 0000..0341
    {"Grek"},  # 0342..0342
    None,  # 0343..0344
    {"Grek"},  # 0345..0345
    None,  # 0346..0362
    {"Latn"},  # 0363..036F
    None,  # 0370..0482
    {"Cyrl", "Perm"},  # 0483..0483
    {"Cyrl", "Glag"},  # 0484..0484
    {"Cyrl", "Latn"},  # 0485..0486
    {"Cyrl", "Glag"},  # 0487..0487
    None,  # 0488..060B
    {"Arab", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 060C..060C
    None,  # 060D..061A
    {"Arab", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 061B..061B
    {"Arab", "Syrc", "Thaa"},  # 061C..061C
    None,  # 061D..061E
    {"Adlm", "Arab", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 061F..061F
    None,  # 0620..063F
    {
        "Adlm",
        "Arab",
        "Mand",
        "Mani",
        "Ougr",
        "Phlp",
        "Rohg",
        "Sogd",
        "Syrc",
    },  # 0640..0640
    None,  # 0641..064A
    {"Arab", "Syrc"},  # 064B..0655
    None,  # 0656..065F
    {"Arab", "Thaa", "Yezi"},  # 0660..0669
    None,  # 066A..066F
    {"Arab", "Syrc"},  # 0670..0670
    None,  # 0671..06D3
    {"Arab", "Rohg"},  # 06D4..06D4
    None,  # 06D5..0950
    {
        "Beng",
        "Deva",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Latn",
        "Mlym",
        "Orya",
        "Shrd",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0951..0951
    {
        "Beng",
        "Deva",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Latn",
        "Mlym",
        "Orya",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0952..0952
    None,  # 0953..0963
    {
        "Beng",
        "Deva",
        "Dogr",
        "Gong",
        "Gonm",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Mahj",
        "Mlym",
        "Nand",
        "Orya",
        "Sind",
        "Sinh",
        "Sylo",
        "Takr",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0964..0964
    {
        "Beng",
        "Deva",
        "Dogr",
        "Gong",
        "Gonm",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Limb",
        "Mahj",
        "Mlym",
        "Nand",
        "Orya",
        "Sind",
        "Sinh",
        "Sylo",
        "Takr",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0965..0965
    {"Deva", "Dogr", "Kthi", "Mahj"},  # 0966..096F
    None,  # 0970..09E5
    {"Beng", "Cakm", "Sylo"},  # 09E6..09EF
    None,  # 09F0..0A65
    {"Guru", "Mult"},  # 0A66..0A6F
    None,  # 0A70..0AE5
    {"Gujr", "Khoj"},  # 0AE6..0AEF
    None,  # 0AF0..0BE5
    {"Gran", "Taml"},  # 0BE6..0BF3
    None,  # 0BF4..0CE5
    {"Knda", "Nand"},  # 0CE6..0CEF
    None,  # 0CF0..103F
    {"Cakm", "Mymr", "Tale"},  # 1040..1049
    None,  # 104A..10FA
    {"Geor", "Latn"},  # 10FB..10FB
    None,  # 10FC..1734
    {"Buhd", "Hano", "Tagb", "Tglg"},  # 1735..1736
    None,  # 1737..1801
    {"Mong", "Phag"},  # 1802..1803
    None,  # 1804..1804
    {"Mong", "Phag"},  # 1805..1805
    None,  # 1806..1CCF
    {"Beng", "Deva", "Gran", "Knda"},  # 1CD0..1CD0
    {"Deva"},  # 1CD1..1CD1
    {"Beng", "Deva", "Gran", "Knda"},  # 1CD2..1CD2
    {"Deva", "Gran"},  # 1CD3..1CD3
    {"Deva"},  # 1CD4..1CD4
    {"Beng", "Deva"},  # 1CD5..1CD6
    {"Deva", "Shrd"},  # 1CD7..1CD7
    {"Beng", "Deva"},  # 1CD8..1CD8
    {"Deva", "Shrd"},  # 1CD9..1CD9
    {"Deva", "Knda", "Mlym", "Orya", "Taml", "Telu"},  # 1CDA..1CDA
    {"Deva"},  # 1CDB..1CDB
    {"Deva", "Shrd"},  # 1CDC..1CDD
    {"Deva"},  # 1CDE..1CDF
    {"Deva", "Shrd"},  # 1CE0..1CE0
    {"Beng", "Deva"},  # 1CE1..1CE1
    {"Deva"},  # 1CE2..1CE8
    {"Deva", "Nand"},  # 1CE9..1CE9
    {"Beng", "Deva"},  # 1CEA..1CEA
    {"Deva"},  # 1CEB..1CEC
    {"Beng", "Deva"},  # 1CED..1CED
    {"Deva"},  # 1CEE..1CF1
    {"Beng", "Deva", "Gran", "Knda", "Nand", "Orya", "Telu", "Tirh"},  # 1CF2..1CF2
    {"Deva", "Gran"},  # 1CF3..1CF3
    {"Deva", "Gran", "Knda"},  # 1CF4..1CF4
    {"Beng", "Deva"},  # 1CF5..1CF6
    {"Beng"},  # 1CF7..1CF7
    {"Deva", "Gran"},  # 1CF8..1CF9
    {"Nand"},  # 1CFA..1CFA
    None,  # 1CFB..1DBF
    {"Grek"},  # 1DC0..1DC1
    None,  # 1DC2..1DF7
    {"Cyrl", "Syrc"},  # 1DF8..1DF8
    None,  # 1DF9..1DF9
    {"Syrc"},  # 1DFA..1DFA
    None,  # 1DFB..202E
    {"Latn", "Mong"},  # 202F..202F
    None,  # 2030..20EF
    {"Deva", "Gran", "Latn"},  # 20F0..20F0
    None,  # 20F1..2E42
    {"Cyrl", "Glag"},  # 2E43..2E43
    None,  # 2E44..3000
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 3001..3002
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3003..3003
    None,  # 3004..3005
    {"Hani"},  # 3006..3006
    None,  # 3007..3007
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 3008..3011
    None,  # 3012..3012
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3013..3013
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 3014..301B
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 301C..301F
    None,  # 3020..3029
    {"Bopo", "Hani"},  # 302A..302D
    None,  # 302E..302F
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3030..3030
    {"Hira", "Kana"},  # 3031..3035
    None,  # 3036..3036
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3037..3037
    None,  # 3038..303B
    {"Hani", "Hira", "Kana"},  # 303C..303D
    {"Hani"},  # 303E..303F
    None,  # 3040..3098
    {"Hira", "Kana"},  # 3099..309C
    None,  # 309D..309F
    {"Hira", "Kana"},  # 30A0..30A0
    None,  # 30A1..30FA
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 30FB..30FB
    {"Hira", "Kana"},  # 30FC..30FC
    None,  # 30FD..318F
    {"Hani"},  # 3190..319F
    None,  # 31A0..31BF
    {"Hani"},  # 31C0..31E3
    None,  # 31E4..321F
    {"Hani"},  # 3220..3247
    None,  # 3248..327F
    {"Hani"},  # 3280..32B0
    None,  # 32B1..32BF
    {"Hani"},  # 32C0..32CB
    None,  # 32CC..32FE
    {"Hani"},  # 32FF..32FF
    None,  # 3300..3357
    {"Hani"},  # 3358..3370
    None,  # 3371..337A
    {"Hani"},  # 337B..337F
    None,  # 3380..33DF
    {"Hani"},  # 33E0..33FE
    None,  # 33FF..A66E
    {"Cyrl", "Glag"},  # A66F..A66F
    None,  # A670..A6FF
    {"Hani", "Latn"},  # A700..A707
    None,  # A708..A82F
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Knda",
        "Kthi",
        "Mahj",
        "Mlym",
        "Modi",
        "Nand",
        "Sind",
        "Takr",
        "Tirh",
    },  # A830..A832
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Knda",
        "Kthi",
        "Mahj",
        "Modi",
        "Nand",
        "Sind",
        "Takr",
        "Tirh",
    },  # A833..A835
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Kthi",
        "Mahj",
        "Modi",
        "Sind",
        "Takr",
        "Tirh",
    },  # A836..A839
    None,  # A83A..A8F0
    {"Beng", "Deva"},  # A8F1..A8F1
    None,  # A8F2..A8F2
    {"Deva", "Taml"},  # A8F3..A8F3
    None,  # A8F4..A92D
    {"Kali", "Latn", "Mymr"},  # A92E..A92E
    None,  # A92F..A9CE
    {"Bugi", "Java"},  # A9CF..A9CF
    None,  # A9D0..FD3D
    {"Arab", "Nkoo"},  # FD3E..FD3F
    None,  # FD40..FDF1
    {"Arab", "Thaa"},  # FDF2..FDF2
    None,  # FDF3..FDFC
    {"Arab", "Thaa"},  # FDFD..FDFD
    None,  # FDFE..FE44
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # FE45..FE46
    None,  # FE47..FF60
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # FF61..FF65
    None,  # FF66..FF6F
    {"Hira", "Kana"},  # FF70..FF70
    None,  # FF71..FF9D
    {"Hira", "Kana"},  # FF9E..FF9F
    None,  # FFA0..100FF
    {"Cpmn", "Cprt", "Linb"},  # 10100..10101
    {"Cprt", "Linb"},  # 10102..10102
    None,  # 10103..10106
    {"Cprt", "Lina", "Linb"},  # 10107..10133
    None,  # 10134..10136
    {"Cprt", "Linb"},  # 10137..1013F
    None,  # 10140..102DF
    {"Arab", "Copt"},  # 102E0..102FB
    None,  # 102FC..10AF1
    {"Mani", "Ougr"},  # 10AF2..10AF2
    None,  # 10AF3..11300
    {"Gran", "Taml"},  # 11301..11301
    None,  # 11302..11302
    {"Gran", "Taml"},  # 11303..11303
    None,  # 11304..1133A
    {"Gran", "Taml"},  # 1133B..1133C
    None,  # 1133D..11FCF
    {"Gran", "Taml"},  # 11FD0..11FD1
    None,  # 11FD2..11FD2
    {"Gran", "Taml"},  # 11FD3..11FD3
    None,  # 11FD4..1BC9F
    {"Dupl"},  # 1BCA0..1BCA3
    None,  # 1BCA4..1D35F
    {"Hani"},  # 1D360..1D371
    None,  # 1D372..1F24F
    {"Hani"},  # 1F250..1F251
    None,  # 1F252..10FFFF
]
