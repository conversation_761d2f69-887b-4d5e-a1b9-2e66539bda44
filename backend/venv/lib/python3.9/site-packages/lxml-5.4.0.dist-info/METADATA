Metadata-Version: 2.4
Name: lxml
Version: 5.4.0
Summary: Powerful and Pythonic XML processing library combining libxml2/libxslt with the ElementTree API.
Home-page: https://lxml.de/
Author: lxml dev team
Author-email: <EMAIL>
Maintainer: lxml dev team
Maintainer-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Source, https://github.com/lxml/lxml
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Cython
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: C
Classifier: Operating System :: OS Independent
Classifier: Topic :: Text Processing :: Markup :: HTML
Classifier: Topic :: Text Processing :: Markup :: XML
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.6
License-File: LICENSE.txt
License-File: LICENSES.txt
Provides-Extra: source
Requires-Dist: Cython<3.1.0,>=3.0.11; extra == "source"
Provides-Extra: cssselect
Requires-Dist: cssselect>=0.7; extra == "cssselect"
Provides-Extra: html5
Requires-Dist: html5lib; extra == "html5"
Provides-Extra: htmlsoup
Requires-Dist: BeautifulSoup4; extra == "htmlsoup"
Provides-Extra: html-clean
Requires-Dist: lxml_html_clean; extra == "html-clean"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary

lxml is a Pythonic, mature binding for the libxml2 and libxslt libraries.  It
provides safe and convenient access to these libraries using the ElementTree
API.

It extends the ElementTree API significantly to offer support for XPath,
RelaxNG, XML Schema, XSLT, C14N and much more.

To contact the project, go to the `project home page
<https://lxml.de/>`_ or see our bug tracker at
https://launchpad.net/lxml

In case you want to use the current in-development version of lxml,
you can get it from the github repository at
https://github.com/lxml/lxml .  Note that this requires Cython to
build the sources, see the build instructions on the project home
page.  To the same end, running ``easy_install lxml==dev`` will
install lxml from
https://github.com/lxml/lxml/tarball/master#egg=lxml-dev if you have
an appropriate version of Cython installed.


After an official release of a new stable series, bug fixes may become
available at
https://github.com/lxml/lxml/tree/lxml-5.4 .
Running ``easy_install lxml==5.4bugfix`` will install
the unreleased branch state from
https://github.com/lxml/lxml/tarball/lxml-5.4#egg=lxml-5.4bugfix
as soon as a maintenance branch has been established.  Note that this
requires Cython to be installed at an appropriate version for the build.

5.4.0 (2025-04-22)
==================

Bugs fixed
----------

* Binary wheels use libxml2 2.13.8 and libxslt 1.1.43 to resolve several CVEs.
  (Binary wheels for Windows continue to use a patched libxml2 2.11.9 and libxslt 1.1.39.)


