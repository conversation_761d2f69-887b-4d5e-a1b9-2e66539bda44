<!DOCTYPE html><html lang="en" dir="ltr" class="lizard-one"><head><meta charset=UTF-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=viewport content="width=device-width,height=device-height,initial-scale=1,user-scalable=no,viewport-fit=cover"><meta name=google content=notranslate><meta name=renderer content=webkit><meta name=Pragma content=no-cache><meta name=mobile-web-app-capable content=yes><meta name=format-detection content="telephone=no"><meta http-equiv=Cache-Control content=no-transform><meta http-equiv=Cache-Control content=no-siteapp><meta name=applicable-device content=pc,mobile><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/5a166dfd2924540647e70e9a952d1fc78c0eadf3602fb05bdbf5f1fd77f87dfc.default.en.s18n-module.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/098d5eb9c6b620833b010c0acb4ea1463fd7e81151d003022814780ef4cf47cf.default.en.s18n.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.@shimo/ui.en.s18n.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.@shimo/common.en.s18n.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.default.en.s18n.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/entry.89e20de923.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/runtime.bd3a8a8c.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/shimo-1e8967897b376294def9907ba8172fbd.a957f2f4.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/shimo-2d94389d97f556edf1dbaf6cc377a670.9abecdb4.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/vendors-1e8967897b376294def9907ba8172fbd.211f849c.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/package-1e8967897b376294def9907ba8172fbd.7b13ca5c.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/package-2d94389d97f556edf1dbaf6cc377a670.aad239b0.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/vendors-2d94389d97f556edf1dbaf6cc377a670.eeebac96.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/lizard-one.17929a1b.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/lib_for_web_and_worker.6e25615a9a.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/sheet.b773f3526a.js" crossorigin=anonymous><link rel=preload as=script href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/mobile-web/js/app-57801459123e610eaeeb.js" crossorigin=anonymous><link rel="shortcut icon" href="/lizard_view/static/favicon.ico"><title>知音楼文档</title><meta name=keywords content=""><meta name=description content=""><script>!function(i,n,e){for(var l in n)i[l]=n[l];for(var l in e)i[l]=e[l]}(window,{"__INITIAL_STATE__":{"user":null},"__LIZARD_CONFIG_HOST__":""},{"__SM_CDN_HOST__":"https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static","__LANG__":"en","__MANIFEST_REVISION__":"8cb31002f","__SCOPE__":"__UNSCOPED__","__RUNTIME_ENV__":{"API_PATH":"/lizard-api","BRAND":"知音楼文档","BRAND_COMPACT":"知音楼文档","CLOUD_FILE_ANONYMOUS_ACCESS":"public","CLOUD_FILE_COLLABORATOR_DISABLED":false,"CLOUD_FILE_SHARE_PUBLIC":true,"COLLAB_NOTIFICATION_CONTROL":true,"CONTENT_HEADER_META_PREFIX":"X-Amz-Meta-","CREATE_FROM_TEMPLATE":true,"DEBUG_SA":"0","DOC_LAYOUT_MODE_CONTROLLER":"TITLE_ENTRY","ENABLE_SERVICE_WORKER":false,"FILE_IMPORT_LIMIT_MB":50,"IS_DEV":"","IS_SQC":0,"MOBILE_DESKTOP_FIXED_LAYOUT":true,"MULTIPART_THRESHOLD":*********,"OBS_DISABLED":false,"OPEN_COMMENT":true,"OTLP_CONSOLE":"","OTLP_URL":"","PD_FORM_LINK_TO_SHEET":true,"PD_FORM_LINK_TO_TABLE":false,"PLUGIN_STORE":"1","PPT_IMPORT_LIMIT_MB":100,"PREVIEW_VIDEO_AUTO_PLAY":false,"PRIVATE_DEPLOY":"1","PRIVATE_DEPLOY_ACCOUNT_SETTINGS":false,"PRIVATE_DEPLOY_ADD_MEMBER":true,"PRIVATE_DEPLOY_FAVICON":null,"PRIVATE_DEPLOY_HIDE_LANGUAGE":1,"PRIVATE_DEPLOY_LOGO":"/uploader/f/EShAWLE3xbiZKxR8.png","PRIVATE_DEPLOY_LOGOUT":false,"PRIVATE_DEPLOY_MANAGE_BOARD":true,"PRIVATE_DEPLOY_MANAGE_DOCX":false,"PRIVATE_DEPLOY_MANAGE_ENTERPRISE":true,"PRIVATE_DEPLOY_MANAGE_MIND_MAP":true,"PRIVATE_DEPLOY_MANAGE_PRESENTATION":true,"PRIVATE_DEPLOY_OPEN_NEWFILE_IN_NEWTAB_DIRECTLY":true,"PRIVATE_DEPLOY_PAGE_TITLE":"知音楼文档","PRIVATE_DEPLOY_TOP_SPACE":1,"PRIVATE_DEPLOY_URL_OF_STYLED_COMPONENTS_2":"/lizard_view/static/lizard-one/styled-components-2.4.1.min.js","QR_SHARE_SCAN_CHANNEL":[],"SDK_ENV":false,"SDK_V2_PATH_PREFIX":"/sdk/v2/api","SELF_HOSTED":"1","SELF_HOSTED_ALLOW_EMAIL_SIGN_UP":true,"SENSORS_DISABLED":true,"SENSORS_SERVER":"https://yach-doc-shimo.zhiyinlou.com/tracking/web/coll.jpg","SENSORS_SERVER_NEW":"https://yach-doc-shimo.zhiyinlou.com/tracking/web/coll.jpg","SENTRY_ENABLE":"","SSE_HOST":"/event","STAT_TRAY_ENABLE":1,"SUPPORT_LOCALES":"zh-CN,en","UPLOADER_HOST":"/uploader/token","UPLOADER_TOKEN_PATH":"/api/upload/token","UPLOADER_TYPE":"aws","URL_REPLACER":"[{\"string\":\"http://minio-service:9000/shimo-assets\",\"replace\":\"https://yach-shimo-pro-shimo-assets-1256037416.cos.ap-beijing.myqcloud.com\",\"withQS\":true},{\"string\":\"http://minio-service:9000/shimo-images\",\"replace\":\"https://yach-shimo-pro-shimo-images-1256037416.cos.ap-beijing.myqcloud.com\",\"withQS\":true},{\"string\":\"http://minio-service:9000/shimo-attachments\",\"replace\":\"https://yach-shimo-pro-shimo-attachments-1256037416.cos.ap-beijing.myqcloud.com\",\"withQS\":true}]","WEBSOCKET_HOST":"/","enableFormCollectUserUniqueInfo":true,"CURRENT_TIME":1750126975213},"__SM_SSR__":true})</script><style>.cg-nav-bar{display:none!important}.cg-nav-bar--dashboard,.cg-popup-view .cg-nav-bar,.cg-transport .cg-nav-bar{display:flex!important}</style><script>
    !function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e,t){return e(t={exports:{}},t.exports),t.exports}var n=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.setupEnv=void 0,t.setupEnv=function(){return window.__COUGAR_ENV__||(window.__COUGAR_ENV__={features:{},deploy:{}}),window.__COUGAR_ENV__}}));t(n),n.setupEnv;var o=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.setupFeature=t.getFeatureForm=t.getFeatureProfile=t.getFeatureSeatsDepleted=t.getFeatureToolbox=t.getFeatureUserCard=t.getFeaturePicker=t.getFeatureWatermark=t.getFeatureReview=t.getFeatureImport=t.getFeatureQuickAccess=t.getFeaturePreview=t.getFeatureTracker=t.getFeatureEditor=t.getFeatureExports=t.getFeatureTenant=t.getFeatureCollaborators=t.getFeatureSearch=t.getFeatureUpload=t.getFeatureDownload=t.getFeatureTheme=t.getFeaturePayment=t.getFeatureFolder=t.getFeatureFileTags=t.getFeatureFileSystem=t.getFeatureShare=t.getFeatureTemplates=t.getFeatureS18n=t.getFeatureNavigation=t.getFeatureDeepLink=t.getFeatureImages=t.getFeatureFeedback=t.getFeatureCreate=t.getFeatureColors=t.getFeatureCloudFile=t.getFeatureBanner=t.getFeatureAccount=void 0,t.getFeatureAccount=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.account},t.getFeatureBanner=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.banner},t.getFeatureCloudFile=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.cloudFile},t.getFeatureColors=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.colors},t.getFeatureCreate=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.create},t.getFeatureFeedback=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.feedback},t.getFeatureImages=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.images},t.getFeatureDeepLink=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.deepLink},t.getFeatureNavigation=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.navigation},t.getFeatureS18n=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.s18n},t.getFeatureTemplates=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.templates},t.getFeatureShare=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.share},t.getFeatureFileSystem=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.fileSystem},t.getFeatureFileTags=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.fileTags},t.getFeatureFolder=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.folder},t.getFeaturePayment=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.payment},t.getFeatureTheme=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.theme},t.getFeatureDownload=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.download},t.getFeatureUpload=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.upload},t.getFeatureSearch=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.search},t.getFeatureCollaborators=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.collaborators},t.getFeatureTenant=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.tenant},t.getFeatureExports=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.exports},t.getFeatureEditor=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.editor},t.getFeatureTracker=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.tracker},t.getFeaturePreview=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.preview},t.getFeatureQuickAccess=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.quickAccess},t.getFeatureImport=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.import},t.getFeatureReview=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.review},t.getFeatureWatermark=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.watermark},t.getFeaturePicker=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.picker},t.getFeatureUserCard=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.userCard},t.getFeatureToolbox=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.toolbox},t.getFeatureSeatsDepleted=function(){var e,t;return null===(t=null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features)||void 0===t?void 0:t.seatsDepleted},t.getFeatureProfile=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.profile},t.getFeatureForm=function(){var e;return null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.features.form},t.setupFeature=function(e,t){(0,n.setupEnv)().features[e]=t}}));t(o),o.setupFeature,o.getFeatureForm,o.getFeatureProfile,o.getFeatureSeatsDepleted,o.getFeatureToolbox,o.getFeatureUserCard,o.getFeaturePicker,o.getFeatureWatermark,o.getFeatureReview,o.getFeatureImport,o.getFeatureQuickAccess,o.getFeaturePreview,o.getFeatureTracker,o.getFeatureEditor,o.getFeatureExports,o.getFeatureTenant,o.getFeatureCollaborators,o.getFeatureSearch,o.getFeatureUpload,o.getFeatureDownload,o.getFeatureTheme,o.getFeaturePayment,o.getFeatureFolder,o.getFeatureFileTags,o.getFeatureFileSystem,o.getFeatureShare,o.getFeatureTemplates,o.getFeatureS18n,o.getFeatureNavigation,o.getFeatureDeepLink,o.getFeatureImages,o.getFeatureFeedback,o.getFeatureCreate,o.getFeatureColors,o.getFeatureCloudFile,o.getFeatureBanner,o.getFeatureAccount;var i=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.setupDeploy=t.getDeployProject=void 0,t.getDeployProject=function(){var e,t;return null===(t=null===(e=window.__COUGAR_ENV__)||void 0===e?void 0:e.deploy)||void 0===t?void 0:t.project},t.setupDeploy=function(e,t){(0,n.setupEnv)().deploy[e]=t}}));t(i),i.setupDeploy,i.getDeployProject;var a=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.setupDeploy=t.getDeployProject=t.getFeatureForm=t.getFeatureProfile=t.getFeatureSeatsDepleted=t.getFeatureToolbox=t.getFeatureUserCard=t.getFeaturePicker=t.getFeatureWatermark=t.getFeatureReview=t.getFeatureImport=t.getFeatureQuickAccess=t.getFeaturePreview=t.getFeatureTracker=t.getFeatureEditor=t.getFeatureExports=t.getFeatureTenant=t.getFeatureUpload=t.getFeatureDownload=t.getFeatureSearch=t.getFeatureTheme=t.getFeaturePayment=t.getFeatureFolder=t.getFeatureFileTags=t.getFeatureFileSystem=t.getFeatureShare=t.getFeatureTemplates=t.getFeatureS18n=t.getFeatureNavigation=t.getFeatureDeepLink=t.getFeatureImages=t.getFeatureFeedback=t.getFeatureCreate=t.getFeatureColors=t.getFeatureCollaborators=t.getFeatureCloudFile=t.getFeatureBanner=t.getFeatureAccount=t.setupFeature=void 0,Object.defineProperty(t,"setupFeature",{enumerable:!0,get:function(){return o.setupFeature}}),Object.defineProperty(t,"getFeatureAccount",{enumerable:!0,get:function(){return o.getFeatureAccount}}),Object.defineProperty(t,"getFeatureBanner",{enumerable:!0,get:function(){return o.getFeatureBanner}}),Object.defineProperty(t,"getFeatureCloudFile",{enumerable:!0,get:function(){return o.getFeatureCloudFile}}),Object.defineProperty(t,"getFeatureCollaborators",{enumerable:!0,get:function(){return o.getFeatureCollaborators}}),Object.defineProperty(t,"getFeatureColors",{enumerable:!0,get:function(){return o.getFeatureColors}}),Object.defineProperty(t,"getFeatureCreate",{enumerable:!0,get:function(){return o.getFeatureCreate}}),Object.defineProperty(t,"getFeatureFeedback",{enumerable:!0,get:function(){return o.getFeatureFeedback}}),Object.defineProperty(t,"getFeatureImages",{enumerable:!0,get:function(){return o.getFeatureImages}}),Object.defineProperty(t,"getFeatureDeepLink",{enumerable:!0,get:function(){return o.getFeatureDeepLink}}),Object.defineProperty(t,"getFeatureNavigation",{enumerable:!0,get:function(){return o.getFeatureNavigation}}),Object.defineProperty(t,"getFeatureS18n",{enumerable:!0,get:function(){return o.getFeatureS18n}}),Object.defineProperty(t,"getFeatureTemplates",{enumerable:!0,get:function(){return o.getFeatureTemplates}}),Object.defineProperty(t,"getFeatureShare",{enumerable:!0,get:function(){return o.getFeatureShare}}),Object.defineProperty(t,"getFeatureFileSystem",{enumerable:!0,get:function(){return o.getFeatureFileSystem}}),Object.defineProperty(t,"getFeatureFileTags",{enumerable:!0,get:function(){return o.getFeatureFileTags}}),Object.defineProperty(t,"getFeatureFolder",{enumerable:!0,get:function(){return o.getFeatureFolder}}),Object.defineProperty(t,"getFeaturePayment",{enumerable:!0,get:function(){return o.getFeaturePayment}}),Object.defineProperty(t,"getFeatureTheme",{enumerable:!0,get:function(){return o.getFeatureTheme}}),Object.defineProperty(t,"getFeatureSearch",{enumerable:!0,get:function(){return o.getFeatureSearch}}),Object.defineProperty(t,"getFeatureDownload",{enumerable:!0,get:function(){return o.getFeatureDownload}}),Object.defineProperty(t,"getFeatureUpload",{enumerable:!0,get:function(){return o.getFeatureUpload}}),Object.defineProperty(t,"getFeatureTenant",{enumerable:!0,get:function(){return o.getFeatureTenant}}),Object.defineProperty(t,"getFeatureExports",{enumerable:!0,get:function(){return o.getFeatureExports}}),Object.defineProperty(t,"getFeatureEditor",{enumerable:!0,get:function(){return o.getFeatureEditor}}),Object.defineProperty(t,"getFeatureTracker",{enumerable:!0,get:function(){return o.getFeatureTracker}}),Object.defineProperty(t,"getFeaturePreview",{enumerable:!0,get:function(){return o.getFeaturePreview}}),Object.defineProperty(t,"getFeatureQuickAccess",{enumerable:!0,get:function(){return o.getFeatureQuickAccess}}),Object.defineProperty(t,"getFeatureImport",{enumerable:!0,get:function(){return o.getFeatureImport}}),Object.defineProperty(t,"getFeatureReview",{enumerable:!0,get:function(){return o.getFeatureReview}}),Object.defineProperty(t,"getFeatureWatermark",{enumerable:!0,get:function(){return o.getFeatureWatermark}}),Object.defineProperty(t,"getFeaturePicker",{enumerable:!0,get:function(){return o.getFeaturePicker}}),Object.defineProperty(t,"getFeatureUserCard",{enumerable:!0,get:function(){return o.getFeatureUserCard}}),Object.defineProperty(t,"getFeatureToolbox",{enumerable:!0,get:function(){return o.getFeatureToolbox}}),Object.defineProperty(t,"getFeatureSeatsDepleted",{enumerable:!0,get:function(){return o.getFeatureSeatsDepleted}}),Object.defineProperty(t,"getFeatureProfile",{enumerable:!0,get:function(){return o.getFeatureProfile}}),Object.defineProperty(t,"getFeatureForm",{enumerable:!0,get:function(){return o.getFeatureForm}}),Object.defineProperty(t,"getDeployProject",{enumerable:!0,get:function(){return i.getDeployProject}}),Object.defineProperty(t,"setupDeploy",{enumerable:!0,get:function(){return i.setupDeploy}})}));t(a),a.setupDeploy,a.getDeployProject,a.getFeatureForm,a.getFeatureProfile,a.getFeatureSeatsDepleted,a.getFeatureToolbox,a.getFeatureUserCard,a.getFeaturePicker,a.getFeatureWatermark,a.getFeatureReview,a.getFeatureImport,a.getFeatureQuickAccess,a.getFeaturePreview,a.getFeatureTracker,a.getFeatureEditor,a.getFeatureExports,a.getFeatureTenant,a.getFeatureUpload,a.getFeatureDownload,a.getFeatureSearch,a.getFeatureTheme,a.getFeaturePayment,a.getFeatureFolder,a.getFeatureFileTags,a.getFeatureFileSystem,a.getFeatureShare,a.getFeatureTemplates,a.getFeatureS18n,a.getFeatureNavigation,a.getFeatureDeepLink,a.getFeatureImages,a.getFeatureFeedback,a.getFeatureCreate,a.getFeatureColors,a.getFeatureCollaborators,a.getFeatureCloudFile,a.getFeatureBanner,a.getFeatureAccount;var u=a.setupFeature,l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAAMFBMVEUAAABAQFBBRUtARkxASEhBRUtARUtBRkpCRktBR0pAQEhARUxARUxBRUtARUpBRkskXCwmAAAAD3RSTlMAIJ+AIO9w36+QEJC/v2BHOlXaAAAAdklEQVQ4y2MY0YDx6n8UECQAlRD/jwYKoRL66BI/oBLx3wRRQP5XqMT/L6h2+v8nKPFDCQX0wyXQAWGJn8YoYD9Oy0eUxDJYeLxCk9gPC7/PuCR+okmshMXSLPJcRSj59KPH7CeohDu6RAlUgkUVVTzGgWH4AAAlS+v/xnjxjgAAAABJRU5ErkJggg==",d="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAAKlBMVEUAAADPz8/S0tLR0dHS0tLS0tLS0tLS0tLS0tLX19fT09PT09PS0tLS0tLrh2MBAAAADXRSTlMAIJ+A75Bw368gEL9g/BD8HwAAAHZJREFUOMtjGEaAc8tdFOA0ASox/S4aqIRK6KJL3IBK+F4TRAG5V6ASdy+i2il7l6DEDSUU0AuXQAeEJa4ao4BYnJaPKIllsPA4hSYRCwu/y7gkrqJJLIXFUhR5riKUfHrRY/YSVEIcXaIQKsGogiruLcAwkgEAiULK+n/1ItAAAAAASUVORK5CYII=",c="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAOVBMVEUAAABARkxBRkpARExBRUtBRktBR0pARUpARUpARUtAQk5AREtAQElARktBRUtDRklARUtAR0lBRksRmzq4AAAAEnRSTlMAgN9A75+QYDC/IHAQr89Qz3DsAgZpAAAA2ElEQVRIx+2Uyw6DIBREecMF1Pb+/8eWIA8J0pKuXDArY84hE5KBrDw2ViuGuMvDTsGSYQmT2k7CjWRucb5nRGwuIMZtokjHDX+BS6rEOwEirkkXF2tC9780bs7iKte6E1h2TKJpotlAIEZlRxlO07egnIyEEN7erFCx4VgoTqW/C9kBRIj0pEAoIiVLWMJDBYH4HgkcUXSCqmuuQl227AR/zmzX9ip4B+fILekNl5a56SR4Dnji1Hd4s2YmZfCUqMseRwM2gcP/fvBfhQ5VpmJkcFiosvJnPi71HBRITzX2AAAAAElFTkSuQmCC",s="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAOVBMVEUAAADR0dHS0tLS0tLS0tLS0tLR0dHS0tLS0tLT09PR0dHU1NTS0tLT09PPz8/T09PS0tLX19fS0tJCl27IAAAAEnRSTlMAgN9A75+QYDC/cBCvzyB/UCBgg/g1AAAA2UlEQVRIx+2UyQ6EIBAFkb1BXPr/P3Y6yiJBopmTB+o0mVTFjsmTDT7LYjRHDGrbX8mKY4Yrs7+Uq8je6iIkRc6OFOtmmaPtxr/ImRKJJoBDN6zBcSSg+T9fLKrn6nTWXcBTY6O9Rpt3AmZ1arQVa/wtJ8F6ASHqNyv1cWE/yE2xH4LYACIk+zkgJsSJjWAEHw0kousFAlE2gS5rLkFZtmoCf84smP0aeAfnyBfWFi4uczYx8ALw1Cff6NWauVLUaVmW3ccAVsDmnz/4Idt0yiusoobTKYM/+QFrkBxGJ9lIbQAAAABJRU5ErkJggg==";u("images",{TabDesktopDesktop:l,TabDesktopDesktopDark:d,TabDesktopSpace:c,TabDesktopSpaceDark:s,TabRecycle:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAPFBMVEUAAABARUxARUtBRUtARExARktBRkpBRktARkpARktBRUtBRktBR0pAREtARUtAQFBAQEBARU1ARUxBRksOzxXNAAAAE3RSTlMAv2C/QKDf3yCA75DfUHAQEDDPoV30UgAAAJhJREFUSMft1MsOwiAQheHTkVoubaGe939XlZh4yQAxRuuCb8GC5F8QBtD9xIFPpoS6jS82NITT9MhhR4EVQQlYpQRuJv2guO6vhRuwUFhyQCGAau8groILyauR1AxCPvpCLvm4rhmMpAGEFMCQYw96cAvcu7MUxdynVdb0Bw+oHHz+azjSHxWeDNDEmQWzhcqO1PjBovuyMwB8G4mTQ7/xAAAAAElFTkSuQmCC",TabRecycleDark:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAOVBMVEUAAADT09PS0tLS0tLQ0NDS0tLS0tLR0dHS0tLS0tLS0tLR0dHR0dHS0tLT09PT09PV1dXPz8/S0tIuq1XTAAAAEnRSTlMAv99gQKAggBDvUHCQj8+QMDDKn4N4AAAAl0lEQVRIx+3UyQ7CIBSF4QOXMpTW4bz/w6rExCEXiDHaLvgWLEj+BeEChr8wfGEj2k58c0bHcrDPAja0smFVAjYpQbBkMorbfq7cgIfCkwaVAKqtg5gFV1JWJ7EbLOXoMzmX44ZuMJEOEFIAR04jGME9CJ/OUhT3mFbJcQcPqB58/2sEMllFIo/QRMsK66HyEzXJeAw/dgE9/xkeMAHZZgAAAABJRU5ErkJggg==",ListDesktop:l,ListDesktopDark:d,ListSpace:c,ListSpaceDark:s,HomeSpace:c,HomeSpaceDark:s});function f(e,t,r,n,o,i){if(!e||/^(?:expires|max\-age|path|domain|secure)$/i.test(e))return!1;var a="";if(r)switch(r.constructor){case Number:a=r===1/0?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+r;break;case String:a="; expires="+r;break;case Date:a="; expires="+r.toUTCString()}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+a+(o?"; domain="+o:"")+(n?"; path="+n:"")+(i?"; secure":""),!0}u("create",[{type:"newdoc",synchronized:!1},{type:"mosheet",synchronized:!1},{type:"form",synchronized:!1},{type:"folder"},{type:"templates"},{type:"upload"}]);for(var g=navigator.userAgent.match(/Yachlang\/(.+?)$/),p=(null==g?void 0:g[1])?g[1]:navigator.language,v=p?[p,navigator.language]:[navigator.language],_="",m=0;m<v.length;m++){var w=v[m];if(null==w?void 0:w.match(/^zh.*$/i)){_="zh-CN";break}if(null==w?void 0:w.match(/^en.*$/i)){_="en";break}}_=_||"en";var b="SM_LOCALE";if(document.documentElement.lang!==_){var A=31536e4;f(b,_,A,"/","."+new URL(location.href).host),f(b,_,A,"/"),location.reload()}var y=/^\s+|\s+$/g,F=/^[-+]0x[0-9a-f]+$/i,S=/^0b[01]+$/i,h=/^0o[0-7]+$/i,P=parseInt,E="object"==typeof e&&e&&e.Object===Object&&e,O="object"==typeof self&&self&&self.Object===Object&&self,M=E||O||Function("return this")(),C=Object.prototype.toString,R=Math.max,I=Math.min,k=function(){return M.Date.now()};function V(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function j(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==C.call(e)}(e))return NaN;if(V(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=V(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(y,"");var r=S.test(e);return r||h.test(e)?P(e.slice(2),r?2:8):F.test(e)?NaN:+e}var U=function(e,t,r){var n,o,i,a,u,l,d=0,c=!1,s=!1,f=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function g(t){var r=n,i=o;return n=o=void 0,d=t,a=e.apply(i,r)}function p(e){return d=e,u=setTimeout(_,t),c?g(e):a}function v(e){var r=e-l;return void 0===l||r>=t||r<0||s&&e-d>=i}function _(){var e=k();if(v(e))return m(e);u=setTimeout(_,function(e){var r=t-(e-l);return s?I(r,i-(e-d)):r}(e))}function m(e){return u=void 0,f&&n?g(e):(n=o=void 0,a)}function w(){var e=k(),r=v(e);if(n=arguments,o=this,l=e,r){if(void 0===u)return p(l);if(s)return u=setTimeout(_,t),g(l)}return void 0===u&&(u=setTimeout(_,t)),a}return t=j(t)||0,V(r)&&(c=!!r.leading,i=(s="maxWait"in r)?R(j(r.maxWait)||0,t):i,f="trailing"in r?!!r.trailing:f),w.cancel=function(){void 0!==u&&clearTimeout(u),d=0,n=l=o=u=void 0},w.flush=function(){return void 0===u?a:m(k())},w},N=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isSMWVAbstractFile=void 0,t.isSMWVAbstractFile=function(e){return"string"==typeof(null==e?void 0:e.uri)}}));t(N),N.isSMWVAbstractFile;var T=function(e,t){return T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},T(e,t)};var D=function(){return D=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},D.apply(this,arguments)};function W(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function B(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a}function L(e){return this instanceof L?(this.v=e,this):new L(e)}var x=Object.freeze({__proto__:null,__extends:function(e,t){function r(){this.constructor=e}T(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)},get __assign(){return D},__rest:function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r},__decorate:function(e,t,r,n){var o,i=arguments.length,a=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var u=e.length-1;u>=0;u--)(o=e[u])&&(a=(i<3?o(a):i>3?o(t,r,a):o(t,r))||a);return i>3&&a&&Object.defineProperty(t,r,a),a},__param:function(e,t){return function(r,n){t(r,n,e)}},__metadata:function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},__awaiter:function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{l(n.next(e))}catch(e){i(e)}}function u(e){try{l(n.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,u)}l((n=n.apply(e,t||[])).next())}))},__generator:function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},__createBinding:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]},__exportStar:function(e,t){for(var r in e)"default"===r||t.hasOwnProperty(r)||(t[r]=e[r])},__values:W,__read:B,__spread:function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(B(arguments[t]));return e},__spreadArrays:function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),o=0;for(t=0;t<r;t++)for(var i=arguments[t],a=0,u=i.length;a<u;a++,o++)n[o]=i[a];return n},__await:L,__asyncGenerator:function(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),i=[];return n={},a("next"),a("throw"),a("return"),n[Symbol.asyncIterator]=function(){return this},n;function a(e){o[e]&&(n[e]=function(t){return new Promise((function(r,n){i.push([e,t,r,n])>1||u(e,t)}))})}function u(e,t){try{(r=o[e](t)).value instanceof L?Promise.resolve(r.value.v).then(l,d):c(i[0][2],r)}catch(e){c(i[0][3],e)}var r}function l(e){u("next",e)}function d(e){u("throw",e)}function c(e,t){e(t),i.shift(),i.length&&u(i[0][0],i[0][1])}},__asyncDelegator:function(e){var t,r;return t={},n("next"),n("throw",(function(e){throw e})),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:L(e[n](t)),done:"return"===n}:o?o(t):t}:o}},__asyncValues:function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=W(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,o){(function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)})(n,o,(t=e[r](t)).done,t.value)}))}}},__makeTemplateObject:function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},__importStar:function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t},__importDefault:function(e){return e&&e.__esModule?e:{default:e}},__classPrivateFieldGet:function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)},__classPrivateFieldSet:function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r}}),G=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.throwNotImplementedError=t.NotImplementedError=void 0;var r=function(e){function t(t){return e.call(this,"".concat(t," not implemented."))||this}return(0,x.__extends)(t,e),t}(Error);t.NotImplementedError=r,t.throwNotImplementedError=function(e){throw new r(e)}}));t(G),G.throwNotImplementedError,G.NotImplementedError;var H=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isAlbumPickerImplemented=t.openAlbumPicker=void 0,t.openAlbumPicker=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.albumPicker;return r?r.open(e):(0,G.throwNotImplementedError)("window.__SMWV__.albumPicker.open")},t.isAlbumPickerImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.albumPicker)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.albumPicker)}}));t(H),H.isAlbumPickerImplemented,H.openAlbumPicker;var Q=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isFileHandlerImplemented=t.openFileHandler=void 0,t.openFileHandler=function(e){var t,r,n=null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.fileHandler)||void 0===r?void 0:r.open;return n?n(e):(0,G.throwNotImplementedError)("window.__SMWV__.fileHandler")},t.isFileHandlerImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.fileHandler)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.fileHandler)}}));t(Q),Q.isFileHandlerImplemented,Q.openFileHandler;var J=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isFilePickerImplemented=t.openFilePicker=void 0,t.openFilePicker=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.filePicker;return r?r.open(e):(0,G.throwNotImplementedError)("window.__SMWV__.filePicker.open")},t.isFilePickerImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.filePicker)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.filePicker)}}));t(J),J.isFilePickerImplemented,J.openFilePicker;var K=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.revoke=t.implement=void 0,t.implement=function(e,t){window.__SMWV__||(window.__SMWV__={}),window.__SMWV__[e]=t},t.revoke=function(e){window.__SMWV__&&(window.__SMWV__[e]=void 0)}}));t(K),K.revoke,K.implement;var Y=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isNavigationImplemented=t.setNavigationEditorState=t.setNavigationButtons=t.setNavigationBack=t.setNavigationTitle=void 0,t.setNavigationTitle=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.navigation;return(null==r?void 0:r.setTitle)?r.setTitle(e):(0,G.throwNotImplementedError)("window.__SMWV__.navigation.setTitle")},t.setNavigationBack=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.navigation;return(null==r?void 0:r.setBack)?r.setBack(e):(0,G.throwNotImplementedError)("window.__SMWV__.navigation.setBack")},t.setNavigationButtons=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.navigation;return(null==r?void 0:r.setButtons)?r.setButtons(e):(0,G.throwNotImplementedError)("window.__SMWV__.navigation.setButtons")},t.setNavigationEditorState=function(e,t){var r,n=null===(r=window.__SMWV__)||void 0===r?void 0:r.navigation;return(null==n?void 0:n.setEditorState)?n.setEditorState(e,t):(0,G.throwNotImplementedError)("window.__SMWV__.navigation.setEditorState")},t.isNavigationImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.navigation)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.navigation)}}));t(Y),Y.isNavigationImplemented,Y.setNavigationEditorState,Y.setNavigationButtons,Y.setNavigationBack,Y.setNavigationTitle;var z=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isS18nImplemented=t.getS18nDir=t.getS18nLang=void 0,t.getS18nLang=function(){var e,t=null===(e=window.__SMWV__)||void 0===e?void 0:e.s18n;return t?t.getLang():(0,G.throwNotImplementedError)("window.__SMWV__.s18n")},t.getS18nDir=function(){var e,t,r=null===(t=null===(e=window.__SMWV__)||void 0===e?void 0:e.s18n)||void 0===t?void 0:t.getDir;return r?r():(0,G.throwNotImplementedError)("window.__SMWV__.getDir")},t.isS18nImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.s18n)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.s18n)}}));t(z),z.isS18nImplemented,z.getS18nDir,z.getS18nLang;var X=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isShareImplemented=t.getShareChannels=t.getSharePayload=t.shareUrl=void 0,t.shareUrl=function(e,t){var r,n,o=null===(n=null===(r=window.__SMWV__)||void 0===r?void 0:r.share)||void 0===n?void 0:n.shareUrl;o?o(e,t):(0,G.throwNotImplementedError)("__SMWV__.share.shareUrl")},t.getSharePayload=function(e){var t,r,n=null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.share)||void 0===r?void 0:r.getPayload;if(n)return n(e);(0,G.throwNotImplementedError)("__SMWV__.share.getPayload")},t.getShareChannels=function(){var e,t,r=null===(t=null===(e=window.__SMWV__)||void 0===e?void 0:e.share)||void 0===t?void 0:t.getChannels;if(r)return r();(0,G.throwNotImplementedError)("__SMWV__.share.getChannels")},t.isShareImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.share)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.share)}}));t(X),X.isShareImplemented,X.getShareChannels,X.getSharePayload,X.shareUrl;var q=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isTransportImplemented=t.getUploadManager=t.getDownloadManager=void 0,t.getDownloadManager=function(){var e,t=null===(e=window.__SMWV__)||void 0===e?void 0:e.transport;return(null==t?void 0:t.download)?t.download:(0,G.throwNotImplementedError)("window.__SMWV__.transport.download")},t.getUploadManager=function(){var e,t=null===(e=window.__SMWV__)||void 0===e?void 0:e.transport;return(null==t?void 0:t.upload)?t.upload:(0,G.throwNotImplementedError)("window.__SMWV__.transport.upload")},t.isTransportImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.transport)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.transport)}}));t(q),q.isTransportImplemented,q.getUploadManager,q.getDownloadManager;var Z=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isUploadImplemented=t.createUploadController=void 0,t.createUploadController=function(e,t){var r,n=null===(r=window.__SMWV__)||void 0===r?void 0:r.upload;return n?n.create(e,t):(0,G.throwNotImplementedError)("window.__SMWV__.upload")},t.isUploadImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.upload)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.upload)}}));t(Z),Z.isUploadImplemented,Z.createUploadController;var $=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isDownloadImplemented=t.createDownloadController=void 0,t.createDownloadController=function(e){var t,r=null===(t=window.__SMWV__)||void 0===t?void 0:t.download;return r?r.create(e):(0,G.throwNotImplementedError)("window.__SMWV__.download")},t.isDownloadImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.download)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.download)}}));t($),$.isDownloadImplemented,$.createDownloadController;var ee=r((function(e,t){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.isClipboardImplemented=t.getClipboardHasString=t.setClipboardString=t.getClipboardString=t.SMWVClipboardSupportedType=void 0,(r=t.SMWVClipboardSupportedType||(t.SMWVClipboardSupportedType={})).Text="text/plain",r.HTML="text/html",t.getClipboardString=function(e){var t,r,n=null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.clipboard)||void 0===r?void 0:r.getString;if(n)return n(e);(0,G.throwNotImplementedError)("__SMWV__.clipboard.getString")},t.setClipboardString=function(e,t){var r,n,o=null===(n=null===(r=window.__SMWV__)||void 0===r?void 0:r.clipboard)||void 0===n?void 0:n.setString;if(o)return o(e,t);(0,G.throwNotImplementedError)("__SMWV__.clipboard.setString")},t.getClipboardHasString=function(){var e,t,r=null===(t=null===(e=window.__SMWV__)||void 0===e?void 0:e.clipboard)||void 0===t?void 0:t.hasString;if(r)return r();(0,G.throwNotImplementedError)("__SMWV__.clipboard.hasString")},t.isClipboardImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.clipboard)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.clipboard)}}));t(ee),ee.isClipboardImplemented,ee.getClipboardHasString,ee.setClipboardString,ee.getClipboardString,ee.SMWVClipboardSupportedType;var te=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isPayImplemented=t.restorePurchase=t.pay=void 0,t.pay=function(e,t){var r,n,o=null===(r=window.__SMWV__)||void 0===r?void 0:r.pay;if(!o)return(0,G.throwNotImplementedError)("window.__SMWV__.pay");null===(n=o.pay)||void 0===n||n.call(o,e,t)},t.restorePurchase=function(){var e,t,r=null===(e=window.__SMWV__)||void 0===e?void 0:e.pay;if(!r)return(0,G.throwNotImplementedError)("window.__SMWV__.pay");null===(t=r.restorePurchase)||void 0===t||t.call(r)},t.isPayImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.pay)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.pay)}}));t(te),te.isPayImplemented,te.restorePurchase,te.pay;var re=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.openProfile=t.isProfileImplemented=void 0,t.isProfileImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.profile)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.profile)},t.openProfile=function(e){var t,r,n=null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.profile)||void 0===r?void 0:r.openProfile;if(n)return n(e);(0,G.throwNotImplementedError)("__SMWV__.profile.openProfile")}}));t(re),re.openProfile,re.isProfileImplemented;var ne=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isThemeImplemented=t.addThemeModeListener=t.getThemeMode=void 0,t.getThemeMode=function(){var e,t,r=null===(t=null===(e=window.__SMWV__)||void 0===e?void 0:e.theme)||void 0===t?void 0:t.getMode;if(r)return r();(0,G.throwNotImplementedError)("__SMWV__.theme.getMode")},t.addThemeModeListener=function(e){var t,r,n=null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.theme)||void 0===r?void 0:r.addModeListener;if(n)return n(e);(0,G.throwNotImplementedError)("__SMWV__.theme.addModeListener")},t.isThemeImplemented=function(e){var t,r,n;return e?!!(null===(r=null===(t=window.__SMWV__)||void 0===t?void 0:t.theme)||void 0===r?void 0:r[e]):!!(null===(n=window.__SMWV__)||void 0===n?void 0:n.theme)}}));t(ne),ne.isThemeImplemented,ne.addThemeModeListener,ne.getThemeMode;var oe=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isThemeImplemented=t.addThemeModeListener=t.getThemeMode=t.openProfile=t.isProfileImplemented=t.isPayImplemented=t.restorePurchase=t.pay=t.isClipboardImplemented=t.SMWVClipboardSupportedType=t.getClipboardHasString=t.getClipboardString=t.isDownloadImplemented=t.createDownloadController=t.isUploadImplemented=t.createUploadController=t.isTransportImplemented=t.getUploadManager=t.getDownloadManager=t.isShareImplemented=t.getSharePayload=t.getShareChannels=t.shareUrl=t.isS18nImplemented=t.getS18nLang=t.getS18nDir=t.isNavigationImplemented=t.setNavigationEditorState=t.setNavigationTitle=t.setNavigationBack=t.setNavigationButtons=t.revoke=t.implement=t.isFilePickerImplemented=t.openFilePicker=t.isFileHandlerImplemented=t.openFileHandler=t.isAlbumPickerImplemented=t.openAlbumPicker=t.isSMWVAbstractFile=void 0,Object.defineProperty(t,"isSMWVAbstractFile",{enumerable:!0,get:function(){return N.isSMWVAbstractFile}}),Object.defineProperty(t,"openAlbumPicker",{enumerable:!0,get:function(){return H.openAlbumPicker}}),Object.defineProperty(t,"isAlbumPickerImplemented",{enumerable:!0,get:function(){return H.isAlbumPickerImplemented}}),Object.defineProperty(t,"openFileHandler",{enumerable:!0,get:function(){return Q.openFileHandler}}),Object.defineProperty(t,"isFileHandlerImplemented",{enumerable:!0,get:function(){return Q.isFileHandlerImplemented}}),Object.defineProperty(t,"openFilePicker",{enumerable:!0,get:function(){return J.openFilePicker}}),Object.defineProperty(t,"isFilePickerImplemented",{enumerable:!0,get:function(){return J.isFilePickerImplemented}}),Object.defineProperty(t,"implement",{enumerable:!0,get:function(){return K.implement}}),Object.defineProperty(t,"revoke",{enumerable:!0,get:function(){return K.revoke}}),Object.defineProperty(t,"setNavigationButtons",{enumerable:!0,get:function(){return Y.setNavigationButtons}}),Object.defineProperty(t,"setNavigationBack",{enumerable:!0,get:function(){return Y.setNavigationBack}}),Object.defineProperty(t,"setNavigationTitle",{enumerable:!0,get:function(){return Y.setNavigationTitle}}),Object.defineProperty(t,"setNavigationEditorState",{enumerable:!0,get:function(){return Y.setNavigationEditorState}}),Object.defineProperty(t,"isNavigationImplemented",{enumerable:!0,get:function(){return Y.isNavigationImplemented}}),Object.defineProperty(t,"getS18nDir",{enumerable:!0,get:function(){return z.getS18nDir}}),Object.defineProperty(t,"getS18nLang",{enumerable:!0,get:function(){return z.getS18nLang}}),Object.defineProperty(t,"isS18nImplemented",{enumerable:!0,get:function(){return z.isS18nImplemented}}),Object.defineProperty(t,"shareUrl",{enumerable:!0,get:function(){return X.shareUrl}}),Object.defineProperty(t,"getShareChannels",{enumerable:!0,get:function(){return X.getShareChannels}}),Object.defineProperty(t,"getSharePayload",{enumerable:!0,get:function(){return X.getSharePayload}}),Object.defineProperty(t,"isShareImplemented",{enumerable:!0,get:function(){return X.isShareImplemented}}),Object.defineProperty(t,"getDownloadManager",{enumerable:!0,get:function(){return q.getDownloadManager}}),Object.defineProperty(t,"getUploadManager",{enumerable:!0,get:function(){return q.getUploadManager}}),Object.defineProperty(t,"isTransportImplemented",{enumerable:!0,get:function(){return q.isTransportImplemented}}),Object.defineProperty(t,"createUploadController",{enumerable:!0,get:function(){return Z.createUploadController}}),Object.defineProperty(t,"isUploadImplemented",{enumerable:!0,get:function(){return Z.isUploadImplemented}}),Object.defineProperty(t,"createDownloadController",{enumerable:!0,get:function(){return $.createDownloadController}}),Object.defineProperty(t,"isDownloadImplemented",{enumerable:!0,get:function(){return $.isDownloadImplemented}}),Object.defineProperty(t,"getClipboardString",{enumerable:!0,get:function(){return ee.getClipboardString}}),Object.defineProperty(t,"getClipboardHasString",{enumerable:!0,get:function(){return ee.getClipboardHasString}}),Object.defineProperty(t,"SMWVClipboardSupportedType",{enumerable:!0,get:function(){return ee.SMWVClipboardSupportedType}}),Object.defineProperty(t,"isClipboardImplemented",{enumerable:!0,get:function(){return ee.isClipboardImplemented}}),Object.defineProperty(t,"pay",{enumerable:!0,get:function(){return te.pay}}),Object.defineProperty(t,"restorePurchase",{enumerable:!0,get:function(){return te.restorePurchase}}),Object.defineProperty(t,"isPayImplemented",{enumerable:!0,get:function(){return te.isPayImplemented}}),Object.defineProperty(t,"isProfileImplemented",{enumerable:!0,get:function(){return re.isProfileImplemented}}),Object.defineProperty(t,"openProfile",{enumerable:!0,get:function(){return re.openProfile}}),Object.defineProperty(t,"getThemeMode",{enumerable:!0,get:function(){return ne.getThemeMode}}),Object.defineProperty(t,"addThemeModeListener",{enumerable:!0,get:function(){return ne.addThemeModeListener}}),Object.defineProperty(t,"isThemeImplemented",{enumerable:!0,get:function(){return ne.isThemeImplemented}})}));t(oe),oe.isThemeImplemented,oe.addThemeModeListener,oe.getThemeMode,oe.openProfile,oe.isProfileImplemented,oe.isPayImplemented,oe.restorePurchase,oe.pay,oe.isClipboardImplemented,oe.SMWVClipboardSupportedType,oe.getClipboardHasString,oe.getClipboardString,oe.isDownloadImplemented,oe.createDownloadController,oe.isUploadImplemented,oe.createUploadController,oe.isTransportImplemented,oe.getUploadManager,oe.getDownloadManager,oe.isShareImplemented,oe.getSharePayload,oe.getShareChannels,oe.shareUrl,oe.isS18nImplemented,oe.getS18nLang,oe.getS18nDir,oe.isNavigationImplemented,oe.setNavigationEditorState,oe.setNavigationTitle,oe.setNavigationBack,oe.setNavigationButtons,oe.revoke;var ie=oe.implement;function ae(e,t){var r=100,n=e;do{n=null==n?void 0:n.parentElement,r-=1}while(r>0&&n&&!t(n));return n}oe.isFilePickerImplemented,oe.openFilePicker,oe.isFileHandlerImplemented,oe.openFileHandler,oe.isAlbumPickerImplemented,oe.openAlbumPicker,oe.isSMWVAbstractFile;var ue,le=U((function(e){console.log("openYachProfile",e),fetch("/lizard-api/user/thirdparty/id?userID="+e).then((function(e){return e.json()})).then((function(t){var r,n,o=t.data.relation[e].id;o&&(null===(n=(r=window.shimo).openProfileHook)||void 0===n||n.call(r,o))})).catch(void 0)}),100),de=function(e,t){var r=ae(e,(function(e){return e.classList.contains("sm-tooltip")}));if(r){r.style.display="none",new MutationObserver((function(){le(t.id)})).observe(r,{attributes:!0,childList:!1,subtree:!1})}else{var n=document.querySelector(".ql-editor");if(n){var o=document.querySelector(".docs-plugin-mention-user-detail");o&&(o.style.visibility="hidden"),le(t.id),n.dispatchEvent(new MouseEvent("mouseover"))}var i=document.querySelector(".mention-detail-container.user-detail");i&&(i.style.visibility="hidden",le(t.id));var a=ae(e,(function(e){return e.classList.contains("sm-sheet-popover")}));a&&(a.style.visibility="hidden",le(t.id));var u=ae(e,(function(e){return e.classList.contains("sm-form__popup_wrapper")}));if(u){u.style.display="none";var l=!0;new MutationObserver((function(){l&&""===u.style.opacity&&le(t.id),l="0"===u.style.opacity})).observe(u,{attributes:!0,childList:!1,subtree:!1})}}};window.shimo=null!==(ue=window.shimo)&&void 0!==ue?ue:{},window.shimo.userCardHook=de,ie("profile",{openProfile:function(e){return le(e),{close:function(){throw new Error("profile close not implemented")},opened:Promise.reject(new Error("profile opened not implemented")),closed:Promise.reject(new Error("profile closed not implemented"))}}});var ce={"《%s》#文件名-云文件(含中文)":"《%s》","《%s》#文件名-目录(含中文)":"《%s》","《%s》#文件名(含中文)":"《%s》","「%s」#文件名-云文件":"「%s」","「%s」#文件名-目录":"「%s」","「%s」#文件名":"「%s」","「":"「","」":"」","添加到知识库":"添加到知识库"},se={"《%s》#文件名-云文件(含中文)":"《%s》","《%s》#文件名-目录(含中文)":"《%s》","《%s》#文件名(含中文)":"《%s》","「%s」#文件名-云文件":"「%s」","「%s」#文件名-目录":"「%s」","「%s」#文件名":"「%s」","「":"「","」":"」","添加到知识库":"Add to Wiki"},fe={"《%s》#文件名-云文件(含中文)":"《%s》","《%s》#文件名-目录(含中文)":"《%s》","《%s》#文件名(含中文)":"《%s》","「%s」#文件名-云文件":"「%s」","「%s」#文件名-目录":"「%s」","「%s」#文件名":"「%s」","「":"「","」":"」","添加到知识库":"إضافة إلى مساحة المعرفة"},ge={"《%s》#文件名-云文件(含中文)":"《%s》","《%s》#文件名-目录(含中文)":"《%s》","《%s》#文件名(含中文)":"《%s》","「%s」#文件名-云文件":"「%s」","「%s」#文件名-目录":"「%s」","「%s」#文件名":"「%s」","「":"「","」":"」","添加到知识库":"知識空間に追加"};var pe=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.throwUndefinedModule=t.isUndefinedModuleError=void 0;var r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,x.__extends)(t,e),t}(Error);t.isUndefinedModuleError=function(e){return e instanceof r},t.throwUndefinedModule=function(e){throw new r("Module: ".concat(e," is not defined on registry."))}}));t(pe),pe.throwUndefinedModule,pe.isUndefinedModuleError;var ve=r((function(e,t){function r(e){var t=(0,x.__assign)({},e),r=function(e,r){return t[e]=r,function(){delete t[e]}};return{isDefined:function(e){return void 0!==t[e]},require:function(e,r){var n=t[e];if("function"==typeof n)return n(r);(0,pe.throwUndefinedModule)(e)},define:function(e){var t,n,o=[],i=Object.keys(e);try{for(var a=(0,x.__values)(i),u=a.next();!u.done;u=a.next()){var l=u.value;o.push(r(l,e[l]))}}catch(e){t={error:e}}finally{try{u&&!u.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return function(){var e,t;try{for(var r=(0,x.__values)(o),n=r.next();!n.done;n=r.next()){(0,n.value)()}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}}},inject:r}}Object.defineProperty(t,"__esModule",{value:!0}),t.getRegistry=t.createRegistry=void 0,t.createRegistry=r,t.getRegistry=function(){var e;return window.__MDRG__?e=window.__MDRG__:(e=r({MenuExtension:void 0,SosContainers:void 0}),window.__MDRG__=e),e}}));t(ve),ve.getRegistry,ve.createRegistry;var _e=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getRegistry=t.createRegistry=void 0,Object.defineProperty(t,"createRegistry",{enumerable:!0,get:function(){return ve.createRegistry}}),Object.defineProperty(t,"getRegistry",{enumerable:!0,get:function(){return ve.getRegistry}})}));t(_e);var me=_e.getRegistry;_e.createRegistry;var we=r((function(e,t){var r;Object.defineProperty(t,"__esModule",{value:!0}),t.EditorSaveStatus=void 0,(r=t.EditorSaveStatus||(t.EditorSaveStatus={})).OFFLINE="offline",r.OFFLINE_SAVING="offlineSaving",r.OFFLINE_SAVED="offlineSaved",r.OFFLINE_SAVE_FAILED="offlineSaveFailed",r.ONLINE="online",r.ONLINE_SAVING="onlineSaving",r.ONLINE_SAVED="onlineSaved",r.ONLINE_SAVE_FAILED="onlineSaveFailed",r.SERVER_CHANGE_APPLIED="serverChangeApplied",r.SERVER_CHANGE_PENDING="serverChangePending"}));t(we),we.EditorSaveStatus;var be=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.throwUndefinedComponent=t.isUndefinedComponentError=void 0;var r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,x.__extends)(t,e),t}(Error);t.isUndefinedComponentError=function(e){return e instanceof r},t.throwUndefinedComponent=function(e){throw new r("Component: ".concat(e," is not defined on registry."))}}));t(be),be.throwUndefinedComponent,be.isUndefinedComponentError;var Ae=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.createRegistry=void 0,t.createRegistry=function(e){var t=Object.keys(e),r=(0,x.__assign)({},e),n=t.reduce((function(e,t){return e[t]=void 0,e}),{}),o=function(e,t){return n[e]&&r[e]!==t&&(n[e]=void 0),r[e]=t,function(){delete n[e],delete r[e]}};return{isDefined:function(e){return void 0!==r[e]},require:function(e,t,o){var i,a=r[t],u=n[t];return u?i=u:a?(i=a(e,o),n[t]=i):(0,be.throwUndefinedComponent)(t),i},define:function(e){var r,n,i=[];try{for(var a=(0,x.__values)(t),u=a.next();!u.done;u=a.next()){var l=u.value;i.push(o(l,e[l]))}}catch(e){r={error:e}}finally{try{u&&!u.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return function(){var e,t;try{for(var r=(0,x.__values)(i),n=r.next();!n.done;n=r.next()){(0,n.value)()}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}}},inject:o}}}));t(Ae),Ae.createRegistry;var ye=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.createRegistry=t.isUndefinedComponentError=void 0,Object.defineProperty(t,"isUndefinedComponentError",{enumerable:!0,get:function(){return be.isUndefinedComponentError}}),Object.defineProperty(t,"createRegistry",{enumerable:!0,get:function(){return Ae.createRegistry}})}));t(ye),ye.createRegistry,ye.isUndefinedComponentError;var Fe=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getRegistry=void 0,t.getRegistry=function(){var e;return window.__EDRG__?e=window.__EDRG__:(e=(0,ye.createRegistry)({EditorToolBar:void 0,EditorLoading:void 0,EditorWrapper:void 0}),window.__EDRG__=e),e}}));t(Fe),Fe.getRegistry;var Se=r((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getRegistry=t.EditorSaveStatus=void 0,Object.defineProperty(t,"EditorSaveStatus",{enumerable:!0,get:function(){return we.EditorSaveStatus}}),Object.defineProperty(t,"getRegistry",{enumerable:!0,get:function(){return Fe.getRegistry}})}));t(Se);var he=Se.getRegistry;Se.EditorSaveStatus;u("navigation",{topBar:{tabs:[{label:"我的桌面",id:"desktop"},{label:"团队空间",id:"space"},{label:"回收站",id:"trash"}]},home:{tabs:[{label:"最近",id:"recent"},{label:"收藏",id:"favorites"},{label:"创建",id:"created"},{label:"共享",id:"shared"}]},editor:{disabled:!0},file:{disabled:!0}}),function(){var e;u("upload",{obs:{disabled:!!(null===(e=window.__RUNTIME_ENV__)||void 0===e?void 0:e.OBS_DISABLED)},uploadInitialHook:function(e){var t,r,n=navigator.userAgent.includes("YachWindow/knowledge");if(n)try{null===(r=null===(t=window.shimo)||void 0===t?void 0:t.openFilePicker)||void 0===r||r.call(t,e)}catch(e){console.error("openFilePicker error",e)}return n}}),window.shimo=window.shimo||{},window.shimo.refreshFolder=window.shimo.refreshFolder||function(e){var t,r,n,o,i=null===(t=window.location.pathname.match(/^\/(folder|space)\/(\w{16})(?:$|\/)/))||void 0===t?void 0:t[2],a="/desktop"===window.location.pathname;if(i===e.guid||a&&"Desktop"===e.guid){var u=me();if(u.isDefined("SosContainers"))null===(o=null===(n=null===(r=u.require("SosContainers").tree)||void 0===r?void 0:r.service)||void 0===n?void 0:n.getChildrenOfFolder)||void 0===o||o.call(n,e.guid);else console.log("SosContainers not defined")}}}(),window.addEventListener("load",(function(){var e,t,r,n,o,i,a,u;null===(t=null===(e=null===window||void 0===window?void 0:window.s18n)||void 0===e?void 0:e.addLocaleResource)||void 0===t||t.call(e,"zh-CN",ce),null===(n=null===(r=null===window||void 0===window?void 0:window.s18n)||void 0===r?void 0:r.addLocaleResource)||void 0===n||n.call(r,"en",se),null===(i=null===(o=null===window||void 0===window?void 0:window.s18n)||void 0===o?void 0:o.addLocaleResource)||void 0===i||i.call(o,"jp",ge),null===(u=null===(a=null===window||void 0===window?void 0:window.s18n)||void 0===a?void 0:a.addLocaleResource)||void 0===u||u.call(a,"ar-SA",fe)})),window.__COUGAR_ENV__&&(window.__COUGAR_ENV__.features.s18n={"ar-SA":fe,"en-US":se,"ja-JP":ge,"zh-CN":ce}),he().inject("EditorLoading",(function(e){return function(e){return e.memo((function(t){var r=t.loading;return e.useEffect((function(){var e,t;r||(console.log("external page loaded"),null===(t=(e=window.shimo).setPageLoaded)||void 0===t||t.call(e))}),[r]),null}))}(e)})),u("collaborators",{mode:"page",groupTab:{disabled:!0},department:{prefetch:{disabled:!0}}}),console.log('"2024-12-24T06:19:10.555Z"-index.js')}();
</script><link href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/sheet.2b9316b679.css" rel=stylesheet crossorigin=anonymous><link href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/mobile-web/css/app-guide-8db165f4c6aea3dad406.css" rel=stylesheet crossorigin=anonymous><link href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/vendors-2d94389d97f556edf1dbaf6cc377a670.156ae2f3.css" rel=stylesheet crossorigin=anonymous><link href="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/lizard-one.2ec2f1c4.css" rel=stylesheet crossorigin=anonymous></head><body class="lizard-one"><div id=root></div><div id=feedback-root></div><script>var perf={startTime:{launch:(new Date).getTime()},trackNow:function(){return this.trackEnd("launch")},trackStart:function(t){this.startTime[t]=(new Date).getTime()},trackEnd:function(t){return(new Date).getTime()-this.startTime[t]}};location.pathname.match(/^\/sheet/)&&window.perf.trackStart("sheet-js-load-time"),window._L||(window._L=function(){});var __LIZARD_FAILED__=!1;window._E||(window._E=function(){__LIZARD_FAILED__||(__LIZARD_FAILED__=!0,console.error("Resource failed to load: "+event.target.src))})</script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/5a166dfd2924540647e70e9a952d1fc78c0eadf3602fb05bdbf5f1fd77f87dfc.default.en.s18n-module.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/098d5eb9c6b620833b010c0acb4ea1463fd7e81151d003022814780ef4cf47cf.default.en.s18n.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.@shimo/ui.en.s18n.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.@shimo/common.en.s18n.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/i18nx/v2/assets/1766a5e7c88553a084c2f912e32c7820434b473837f02253ec038d90ba257ad3.default.en.s18n.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script>!function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){var n={exports:{}};return e(n,n.exports),n.exports}var r=n((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.compile=void 0;var r="undefined"!=typeof Symbol?Symbol.for("s18n-compiled"):"__$s18n-compiled";n.compile=function(e,n,o){return function(e,n){for(var o=n,t=0;t<e.length&&o;t++)o=o[e[t]];return o&&o[r]}(e,o)||function(e,n,o){for(var t=o,c=0;c<e.length&&t;c++)t=t[e[c]]||(t[e[c]]={});return t[r]=n,n}(e,function(e,n){var r=e.length,o=e.map((function(e,n){return n===r-1?e:"".concat(e,"%").concat(2===r?"s":n+1)})).join(""),t=n[o];return("string"==typeof t?t:o).split(/(%s|%\d)/g).map((function(e){var n,r=null===(n=/^%(s|\d)$/g.exec(e))||void 0===n?void 0:n[1];return r?"s"===r?1:Number(r):e}))}(e,n),o)}})),o=n((function(n,o){Object.defineProperty(o,"__esModule",{value:!0}),o.getS18n=void 0;var t=("undefined"!=typeof navigator&&navigator.languages||[])[0]||"en-US",c="__$s18n_namespace_".concat(Date.now(),"_").concat(String(Math.random()).slice(2)),a="__$s18n-global_resource_".concat("3","__"),i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e?e:(console.warn("[s18n] cannot resolve globalThis, sharing by namespace is disabled"),{}),u=i[a]||(i[a]={}),l=function(e,n){var o=u[e]||(u[e]={currentLocale:t,cache:{},resources:{}}),c=function(){o.cache={}},a=function(e){if("string"!=typeof e)throw new TypeError("'locale' is expect to be a string");o.currentLocale!==e&&(o.currentLocale=e,c())},i=function(){var r=o.resources[o.currentLocale]||(o.resources[o.currentLocale]={});if(null!=n){var t=r[n]||(r[n]={});t&&"object"==typeof t?r=t:(console.error("[s18n] resource at ".concat(e,"::").concat(n," is not an object:"),o.resources),r={})}return r},s=function(){var e=o.cache;if(null!=n){var r=e[n]||(e[n]={});r&&"object"==typeof r?e=r:(console.error("[s18n] cache at ".concat(e,"::").concat(n," is not an object:"),o.resources),e={})}return e};function f(e){var n=i(),o=s();if("string"==typeof e){var t=n[e];return"string"==typeof t?t:e}if(e&&"number"==typeof e.length){if(1===e.length)return f(e[0]);for(var c=(0,r.compile)(e,n,o),a=arguments,u="",l=0;l<c.length;l++){var d=c[l];u+="number"==typeof d?String(a[d]):d}return u}return console.warn("[s18n] invalid input:",arguments),String(e)}return f.x=function(e){var n=i(),o=s();if("number"==typeof e.length){if(1===e.length)return[f(e)];for(var t=(0,r.compile)(e,n,o),c=arguments,a=[],u=0;u<t.length;u++){var l=t[u];a.push("number"==typeof l?c[l]:l)}return a}return console.warn("[s18n] invalid input:",arguments),[e]},f.addLocaleResource=function(e,n){o.resources[e]?Object.assign(o.resources[e],n):o.resources[e]=n,c()},f.setLocale=a,f.setLocaleSync=a,f.getLocale=function(){return o.currentLocale||t},f.getS18n=l,f.DEBUG_reset=function(){Object.keys(u).forEach((function(e){var n=u[e];n.currentLocale=t,n.resources={},n.cache={}})),c()},f.DEBUG_print=function(){var e={ns:o,resource:i(),root:u};return console.log(JSON.stringify(e,null,2)),e},f};o.getS18n=l;var s=l(c);o.default=s})),t=(e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(o);"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),r=1;r<arguments.length;r++){var o=arguments[r];if(null!=o)for(var t in o)Object.prototype.hasOwnProperty.call(o,t)&&(n[t]=o[t])}return n},writable:!0,configurable:!0});var c,a,i=window.lizardLocaleMessages;window.s18n=t.default,c="ja"===document.documentElement.lang?"jp":document.documentElement.lang,a=function(){if(void 0===i||!Array.isArray(i)||0===i.length)return{};for(var e={},n=0,r=i;n<r.length;n++){var o=r[n];for(var t in o)if(o.hasOwnProperty(t))for(var c in o[t])o[t].hasOwnProperty(c)&&(e[c]=o[t][c]||e[c])}return e}(),t.default.setLocale(c),t.default.addLocaleResource(c,a)}();
</script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/entry.89e20de923.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/runtime.bd3a8a8c.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/shimo-1e8967897b376294def9907ba8172fbd.a957f2f4.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/shimo-2d94389d97f556edf1dbaf6cc377a670.9abecdb4.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/vendors-1e8967897b376294def9907ba8172fbd.211f849c.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/package-1e8967897b376294def9907ba8172fbd.7b13ca5c.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/package-2d94389d97f556edf1dbaf6cc377a670.aad239b0.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/vendors-2d94389d97f556edf1dbaf6cc377a670.eeebac96.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/lizard-one/lizard-one.17929a1b.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/lib_for_web_and_worker.6e25615a9a.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/sheet/sheet.b773f3526a.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script src="https://yach-doc-shimo-cdn.zhiyinlou.com/lizard_view/static/static/mobile-web/js/app-57801459123e610eaeeb.js" crossorigin=anonymous onerror=_E(event) onload=_L(event)></script><script> if (/^\/(docs|sheets|presentation|slides|docx|forms|mindmaps|boards)/.test(location.pathname)) {
  var style = document.createElement('style');
  style.innerHTML = '#root > [class^=Page] > [class^=StyleWrapper] {overflow: auto; max-height: calc(100% - 100px);}'
  if (location.href.indexOf('docs') > -1) {
    style.innerHTML += 'body { overflow:hidden; }'
  }
  document.body.appendChild(style);
} </script> <script type='text/javascript' src='https://yach-static.zhiyinlou.com/web_static/shimo/shimo_custom.js?timestamp=1748870359420'></script> <script> window.Notification = undefined </script> <script>
    !function(e){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=n(e);function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e,n){return e(n={exports:{}},n.exports),n.exports}var i=o((function(e){!function(){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t={rotl:function(e,n){return e<<n|e>>>32-n},rotr:function(e,n){return e<<32-n|e>>>n},endian:function(e){if(e.constructor==Number)return 16711935&t.rotl(e,8)|4278255360&t.rotl(e,24);for(var n=0;n<e.length;n++)e[n]=t.endian(e[n]);return e},randomBytes:function(e){for(var n=[];e>0;e--)n.push(Math.floor(256*Math.random()));return n},bytesToWords:function(e){for(var n=[],t=0,r=0;t<e.length;t++,r+=8)n[r>>>5]|=e[t]<<24-r%32;return n},wordsToBytes:function(e){for(var n=[],t=0;t<32*e.length;t+=8)n.push(e[t>>>5]>>>24-t%32&255);return n},bytesToHex:function(e){for(var n=[],t=0;t<e.length;t++)n.push((e[t]>>>4).toString(16)),n.push((15&e[t]).toString(16));return n.join("")},hexToBytes:function(e){for(var n=[],t=0;t<e.length;t+=2)n.push(parseInt(e.substr(t,2),16));return n},bytesToBase64:function(e){for(var t=[],r=0;r<e.length;r+=3)for(var o=e[r]<<16|e[r+1]<<8|e[r+2],i=0;i<4;i++)8*r+6*i<=8*e.length?t.push(n.charAt(o>>>6*(3-i)&63)):t.push("=");return t.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var t=[],r=0,o=0;r<e.length;o=++r%4)0!=o&&t.push((n.indexOf(e.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|n.indexOf(e.charAt(r))>>>6-2*o);return t}};e.exports=t}()})),a={utf8:{stringToBytes:function(e){return a.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(a.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var n=[],t=0;t<e.length;t++)n.push(255&e.charCodeAt(t));return n},bytesToString:function(e){for(var n=[],t=0;t<e.length;t++)n.push(String.fromCharCode(e[t]));return n.join("")}}},u=a,c=function(e){return null!=e&&(l(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&l(e.slice(0,0))}(e)||!!e._isBuffer)};function l(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var f,s,d=o((function(e){!function(){var n=i,t=u.utf8,r=c,o=u.bin,a=function(e,i){e.constructor==String?e=i&&"binary"===i.encoding?o.stringToBytes(e):t.stringToBytes(e):r(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var u=n.bytesToWords(e),c=8*e.length,l=1732584193,f=-271733879,s=-1732584194,d=271733878,p=0;p<u.length;p++)u[p]=16711935&(u[p]<<8|u[p]>>>24)|4278255360&(u[p]<<24|u[p]>>>8);u[c>>>5]|=128<<c%32,u[14+(c+64>>>9<<4)]=c;var y=a._ff,h=a._gg,v=a._hh,w=a._ii;for(p=0;p<u.length;p+=16){var m=l,g=f,_=s,b=d;l=y(l,f,s,d,u[p+0],7,-680876936),d=y(d,l,f,s,u[p+1],12,-389564586),s=y(s,d,l,f,u[p+2],17,606105819),f=y(f,s,d,l,u[p+3],22,-1044525330),l=y(l,f,s,d,u[p+4],7,-176418897),d=y(d,l,f,s,u[p+5],12,1200080426),s=y(s,d,l,f,u[p+6],17,-1473231341),f=y(f,s,d,l,u[p+7],22,-45705983),l=y(l,f,s,d,u[p+8],7,1770035416),d=y(d,l,f,s,u[p+9],12,-1958414417),s=y(s,d,l,f,u[p+10],17,-42063),f=y(f,s,d,l,u[p+11],22,-1990404162),l=y(l,f,s,d,u[p+12],7,1804603682),d=y(d,l,f,s,u[p+13],12,-40341101),s=y(s,d,l,f,u[p+14],17,-1502002290),l=h(l,f=y(f,s,d,l,u[p+15],22,1236535329),s,d,u[p+1],5,-165796510),d=h(d,l,f,s,u[p+6],9,-1069501632),s=h(s,d,l,f,u[p+11],14,643717713),f=h(f,s,d,l,u[p+0],20,-373897302),l=h(l,f,s,d,u[p+5],5,-701558691),d=h(d,l,f,s,u[p+10],9,38016083),s=h(s,d,l,f,u[p+15],14,-660478335),f=h(f,s,d,l,u[p+4],20,-405537848),l=h(l,f,s,d,u[p+9],5,568446438),d=h(d,l,f,s,u[p+14],9,-1019803690),s=h(s,d,l,f,u[p+3],14,-187363961),f=h(f,s,d,l,u[p+8],20,1163531501),l=h(l,f,s,d,u[p+13],5,-1444681467),d=h(d,l,f,s,u[p+2],9,-51403784),s=h(s,d,l,f,u[p+7],14,1735328473),l=v(l,f=h(f,s,d,l,u[p+12],20,-1926607734),s,d,u[p+5],4,-378558),d=v(d,l,f,s,u[p+8],11,-2022574463),s=v(s,d,l,f,u[p+11],16,1839030562),f=v(f,s,d,l,u[p+14],23,-35309556),l=v(l,f,s,d,u[p+1],4,-1530992060),d=v(d,l,f,s,u[p+4],11,1272893353),s=v(s,d,l,f,u[p+7],16,-155497632),f=v(f,s,d,l,u[p+10],23,-1094730640),l=v(l,f,s,d,u[p+13],4,681279174),d=v(d,l,f,s,u[p+0],11,-358537222),s=v(s,d,l,f,u[p+3],16,-722521979),f=v(f,s,d,l,u[p+6],23,76029189),l=v(l,f,s,d,u[p+9],4,-640364487),d=v(d,l,f,s,u[p+12],11,-421815835),s=v(s,d,l,f,u[p+15],16,530742520),l=w(l,f=v(f,s,d,l,u[p+2],23,-995338651),s,d,u[p+0],6,-198630844),d=w(d,l,f,s,u[p+7],10,1126891415),s=w(s,d,l,f,u[p+14],15,-1416354905),f=w(f,s,d,l,u[p+5],21,-57434055),l=w(l,f,s,d,u[p+12],6,1700485571),d=w(d,l,f,s,u[p+3],10,-1894986606),s=w(s,d,l,f,u[p+10],15,-1051523),f=w(f,s,d,l,u[p+1],21,-2054922799),l=w(l,f,s,d,u[p+8],6,1873313359),d=w(d,l,f,s,u[p+15],10,-30611744),s=w(s,d,l,f,u[p+6],15,-1560198380),f=w(f,s,d,l,u[p+13],21,1309151649),l=w(l,f,s,d,u[p+4],6,-145523070),d=w(d,l,f,s,u[p+11],10,-1120210379),s=w(s,d,l,f,u[p+2],15,718787259),f=w(f,s,d,l,u[p+9],21,-343485551),l=l+m>>>0,f=f+g>>>0,s=s+_>>>0,d=d+b>>>0}return n.endian([l,f,s,d])};a._ff=function(e,n,t,r,o,i,a){var u=e+(n&t|~n&r)+(o>>>0)+a;return(u<<i|u>>>32-i)+n},a._gg=function(e,n,t,r,o,i,a){var u=e+(n&r|t&~r)+(o>>>0)+a;return(u<<i|u>>>32-i)+n},a._hh=function(e,n,t,r,o,i,a){var u=e+(n^t^r)+(o>>>0)+a;return(u<<i|u>>>32-i)+n},a._ii=function(e,n,t,r,o,i,a){var u=e+(t^(n|~r))+(o>>>0)+a;return(u<<i|u>>>32-i)+n},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=n.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?o.bytesToString(r):n.bytesToHex(r)}}()})),p=function(){return p=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},p.apply(this,arguments)},y=function(){function e(){this.injectedComponentsMap={}}return e.prototype.proxy=function(e,n){var r=this.injectedComponentsMap;return function(o){var i,a=r[e],u=null!==(i=null==a?void 0:a({originComponentType:n}))&&void 0!==i?i:n;return t.default.createElement(u,p({},o))}},e.prototype.inject=function(e,n){var t=this.encryptPath(e);this.injectedComponentsMap[t]=n},e.prototype.encryptPath=function(e){return d(e)},e}();window.shimo=null!==(f=window.shimo)&&void 0!==f?f:{};var h=window.shimo;h.lizardInjecor=null!==(s=h.lizardInjecor)&&void 0!==s?s:new y;var v=h.lizardInjecor,w={display:"inline-block",width:"28px",height:"28px",marginRight:"10px",borderRadius:"14px",overflow:"hidden",minWidth:"28px",verticalAlign:"middle"},m={width:"32px",height:"32px",marginRight:"10px",borderRadius:"16px"};function g(e){var n,t;return null===(t=null===(n=window.shimo)||void 0===n?void 0:n.openProfileHook)||void 0===t?void 0:t.call(n,e)}function _(e){(function(e){return fetch("/lizard-api/user/thirdparty/id?userID=".concat(e),{method:"GET"}).then((function(e){return e.json()})).then((function(n){var t,r,o;return null===(o=null===(r=null===(t=n.data)||void 0===t?void 0:t.relation)||void 0===r?void 0:r[e])||void 0===o?void 0:o.id})).catch((function(e){throw alert("Request user info error"),e}))})(e).then((function(n){n?g(n):alert("Get empty employee id ".concat(n," by ").concat(e))}))}!function(){var e;window.shimo=null!==(e=window.shimo)&&void 0!==e?e:{},window.shimo.openNameCardByEmployeeId=g,window.shimo.openNameCardByUserId=_,/^[a-zA-Z0-9]{16}/.test(window.location.pathname.substring(1,17))&&window.location.pathname.substring(17).startsWith("/collaborators")&&v.inject("/packages/shared/lo-collab-panel/components/avatar/avatar_user.tsx#AvatarUser",(function(){return function(e){var n,r=e.src,o=e.userId,i=e.width,a=void 0===i?28:i,u=e.height,c=void 0===u?28:u,l=e.newSearchList,f=!!(null===(n=window.__RUNTIME_ENV__)||void 0===n?void 0:n.FIELD_MANAGEMENT),s=f&&l?m:w,d=function(){_(o)};return f?t.default.createElement("div",{style:s,"data-test":"avatar_user",onClick:d,"data-inject":"test"},t.default.createElement("img",{src:r,width:l?a:28,height:l?c:28})):t.default.createElement("div",{style:s,"data-test":"avatar_user",onClick:d,"data-inject":"test"},t.default.createElement("img",{src:r,width:28,height:28}))}}))}();var b=function(e,n){return b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t])},b(e,n)};var C=function(){return C=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},C.apply(this,arguments)};function x(e){var n="function"==typeof Symbol&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function O(e,n){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,o,i=t.call(e),a=[];try{for(;(void 0===n||n-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(t=i.return)&&t.call(i)}finally{if(o)throw o.error}}return a}function j(e){return this instanceof j?(this.v=e,this):new j(e)}var S=Object.freeze({__proto__:null,__extends:function(e,n){function t(){this.constructor=e}b(e,n),e.prototype=null===n?Object.create(n):(t.prototype=n.prototype,new t)},get __assign(){return C},__rest:function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]])}return t},__decorate:function(e,n,t,r){var o,i=arguments.length,a=i<3?n:null===r?r=Object.getOwnPropertyDescriptor(n,t):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,n,t,r);else for(var u=e.length-1;u>=0;u--)(o=e[u])&&(a=(i<3?o(a):i>3?o(n,t,a):o(n,t))||a);return i>3&&a&&Object.defineProperty(n,t,a),a},__param:function(e,n){return function(t,r){n(t,r,e)}},__metadata:function(e,n){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,n)},__awaiter:function(e,n,t,r){return new(t||(t=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function u(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var n;e.done?o(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(a,u)}c((r=r.apply(e,n||[])).next())}))},__generator:function(e,n){var t,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;a;)try{if(t=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=n.call(e,a)}catch(e){i=[6,e],r=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},__createBinding:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]},__exportStar:function(e,n){for(var t in e)"default"===t||n.hasOwnProperty(t)||(n[t]=e[t])},__values:x,__read:O,__spread:function(){for(var e=[],n=0;n<arguments.length;n++)e=e.concat(O(arguments[n]));return e},__spreadArrays:function(){for(var e=0,n=0,t=arguments.length;n<t;n++)e+=arguments[n].length;var r=Array(e),o=0;for(n=0;n<t;n++)for(var i=arguments[n],a=0,u=i.length;a<u;a++,o++)r[o]=i[a];return r},__await:j,__asyncGenerator:function(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=t.apply(e,n||[]),i=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(e){o[e]&&(r[e]=function(n){return new Promise((function(t,r){i.push([e,n,t,r])>1||u(e,n)}))})}function u(e,n){try{(t=o[e](n)).value instanceof j?Promise.resolve(t.value.v).then(c,l):f(i[0][2],t)}catch(e){f(i[0][3],e)}var t}function c(e){u("next",e)}function l(e){u("throw",e)}function f(e,n){e(n),i.shift(),i.length&&u(i[0][0],i[0][1])}},__asyncDelegator:function(e){var n,t;return n={},r("next"),r("throw",(function(e){throw e})),r("return"),n[Symbol.iterator]=function(){return this},n;function r(r,o){n[r]=e[r]?function(n){return(t=!t)?{value:j(e[r](n)),done:"return"===r}:o?o(n):n}:o}},__asyncValues:function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,t=e[Symbol.asyncIterator];return t?t.call(e):(e=x(e),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(t){n[t]=e[t]&&function(n){return new Promise((function(r,o){(function(e,n,t,r){Promise.resolve(r).then((function(n){e({value:n,done:t})}),n)})(r,o,(n=e[t](n)).done,n.value)}))}}},__makeTemplateObject:function(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e},__importStar:function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)Object.hasOwnProperty.call(e,t)&&(n[t]=e[t]);return n.default=e,n},__importDefault:function(e){return e&&e.__esModule?e:{default:e}},__classPrivateFieldGet:function(e,n){if(!n.has(e))throw new TypeError("attempted to get private field on non-instance");return n.get(e)},__classPrivateFieldSet:function(e,n,t){if(!n.has(e))throw new TypeError("attempted to set private field on non-instance");return n.set(e,t),t}}),T=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.throwUndefinedModule=n.isUndefinedModuleError=void 0;var t=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return(0,S.__extends)(n,e),n}(Error);n.isUndefinedModuleError=function(e){return e instanceof t},n.throwUndefinedModule=function(e){throw new t("Module: ".concat(e," is not defined on registry."))}}));r(T),T.throwUndefinedModule,T.isUndefinedModuleError;var M=o((function(e,n){function t(e){var n=(0,S.__assign)({},e),t=function(e,t){return n[e]=t,function(){delete n[e]}};return{isDefined:function(e){return void 0!==n[e]},require:function(e,t){var r=n[e];if("function"==typeof r)return r(t);(0,T.throwUndefinedModule)(e)},define:function(e){var n,r,o=[],i=Object.keys(e);try{for(var a=(0,S.__values)(i),u=a.next();!u.done;u=a.next()){var c=u.value;o.push(t(c,e[c]))}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return function(){var e,n;try{for(var t=(0,S.__values)(o),r=t.next();!r.done;r=t.next()){(0,r.value)()}}catch(n){e={error:n}}finally{try{r&&!r.done&&(n=t.return)&&n.call(t)}finally{if(e)throw e.error}}}},inject:t}}Object.defineProperty(n,"__esModule",{value:!0}),n.getRegistry=n.createRegistry=void 0,n.createRegistry=t,n.getRegistry=function(){var e;return window.__MDRG__?e=window.__MDRG__:(e=t({MenuExtension:void 0,SosContainers:void 0}),window.__MDRG__=e),e}}));r(M),M.getRegistry,M.createRegistry;var R=o((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.getRegistry=n.createRegistry=void 0,Object.defineProperty(n,"createRegistry",{enumerable:!0,get:function(){return M.createRegistry}}),Object.defineProperty(n,"getRegistry",{enumerable:!0,get:function(){return M.getRegistry}})}));r(R);var E,k,P,B,H=R.getRegistry;R.createRegistry,function(e){e[e.Unknown=0]="Unknown",e[e.FolderOrSpace=1]="FolderOrSpace",e[e.ShimoFile=2]="ShimoFile",e[e.CloudFile=3]="CloudFile"}(E||(E={})),function(e){e[e.shortcut=-12]="shortcut",e[e.table=-11]="table",e[e.presentation=-10]="presentation",e[e.board=-9]="board",e[e.form=-8]="form",e[e.mindmap=-7]="mindmap",e[e.modoc=-6]="modoc",e[e.mosheet=-4]="mosheet",e[e.newdoc=-2]="newdoc",e[e.document=0]="document",e[e.folder=1]="folder",e[e.space=2]="space",e[e.img=3]="img",e[e.pdf=4]="pdf",e[e.xls=5]="xls",e[e.docx=6]="docx",e[e.ppt=7]="ppt",e[e.mp3=8]="mp3",e[e.zip=9]="zip",e[e.mp4=10]="mp4",e[e.wps=11]="wps",e[e.xmind=12]="xmind",e[e.unknown=-999]="unknown"}(k||(k={})),function(e){e.owner="owner",e.editor="editor",e.commentator="commentator",e.reader="reader",e.unknown="unknown",e.inherited="inherited",e.none="none"}(P||(P={})),function(e){e.owner="owner",e.editor="editor",e.commentator="commentator",e.reader="reader",e.unknown="unknown_role",e.inherited="inherited",e.none="none"}(B||(B={}));var I=function(){return t.default.createElement("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.14028 12.0857C7.04112 12.0588 6.93669 12.0444 6.82886 12.0444H3.31513C2.9917 12.0444 2.72951 11.7857 2.72951 11.4666V3.37775C2.72951 3.05866 2.9917 2.79998 3.31513 2.79998H4.23539V5.19998L5.3648 4.39998L6.49422 5.19998V2.79998H6.82886C6.94259 2.79998 7.05254 2.81734 7.15651 2.84944C7.6439 2.9999 8.0001 3.47399 8.0001 3.99998C8.0001 3.47399 8.35629 2.9999 8.84368 2.84944C8.94766 2.81734 9.05761 2.79998 9.17134 2.79998H12.6851C13.0085 2.79998 13.2707 3.05866 13.2707 3.37775V11.4666C13.2707 11.7857 13.0085 12.0444 12.6851 12.0444H9.17134C9.06351 12.0444 8.95908 12.0588 8.85991 12.0857C8.36422 12.2203 8.0001 12.6682 8.0001 13.2C8.0001 12.6682 7.63597 12.2203 7.14028 12.0857ZM9.12846 13.2521C9.10277 13.8907 8.60741 14.4 8.0001 14.4C7.39278 14.4 6.89742 13.8907 6.87173 13.2521C6.86199 13.2481 6.84789 13.2444 6.82886 13.2444H3.31513C2.44822 13.2444 1.6001 12.5307 1.6001 11.4666V3.37775C1.6001 2.31372 2.44822 1.59998 3.31513 1.59998H6.82886C7.25286 1.59998 7.65457 1.72419 8.0001 1.9403C8.34563 1.72419 8.74734 1.59998 9.17134 1.59998H12.6851C13.552 1.59998 14.4001 2.31372 14.4001 3.37775V11.4666C14.4001 12.5307 13.552 13.2444 12.6851 13.2444H9.17134C9.1523 13.2444 9.13821 13.2481 9.12846 13.2521ZM10.106 4.39998C9.77461 4.39998 9.50598 4.6686 9.50598 4.99998C9.50598 5.33135 9.77461 5.59998 10.106 5.59998H11.1648C11.4962 5.59998 11.7648 5.33135 11.7648 4.99998C11.7648 4.6686 11.4962 4.39998 11.1648 4.39998H10.106ZM10.2589 9.79998C10.2589 9.4686 10.5275 9.19998 10.8589 9.19998H11.1648C11.4962 9.19998 11.7648 9.4686 11.7648 9.79998C11.7648 10.1313 11.4962 10.4 11.1648 10.4H10.8589C10.5275 10.4 10.2589 10.1313 10.2589 9.79998ZM4.83539 9.19998C4.50402 9.19998 4.23539 9.4686 4.23539 9.79998C4.23539 10.1313 4.50402 10.4 4.83539 10.4H5.14127C5.47265 10.4 5.74127 10.1313 5.74127 9.79998C5.74127 9.4686 5.47265 9.19998 5.14127 9.19998H4.83539ZM9.50598 7.39998C9.50598 7.0686 9.77461 6.79998 10.106 6.79998H11.1648C11.4962 6.79998 11.7648 7.0686 11.7648 7.39998C11.7648 7.73135 11.4962 7.99998 11.1648 7.99998H10.106C9.77461 7.99998 9.50598 7.73135 9.50598 7.39998ZM4.83539 6.79998C4.50402 6.79998 4.23539 7.0686 4.23539 7.39998C4.23539 7.73135 4.50402 7.99998 4.83539 7.99998H5.89422C6.22559 7.99998 6.49422 7.73135 6.49422 7.39998C6.49422 7.0686 6.22559 6.79998 5.89422 6.79998H4.83539Z",fill:"#41464B",fillOpacity:"0.9"}))},A=function(e){var n,t=e.files,r=e.menu;if(1!==t.length)return[];var o=t[0],i=o.guid,a=o.type,u=Object.values(k),c=Object.keys(k),l=null===(n=window.shimo)||void 0===n?void 0:n.onAddKnowledgeBase,f=function(){var e,n,t,r=null!==(e=function(e){if(c.includes(e))return e;var n=u.findIndex((function(n){return n===e}));return n?c[n]:"unknown"}(a))&&void 0!==e?e:"unknown",f=function(e){var n=C({},e);return n.type<=0?(n.subType=n.type,n.type=E.ShimoFile,n):1===n.type?(n.subType=n.isSpace?k.space:k.folder,n.type=E.FolderOrSpace,n):2===n.type?(n.extType=n.name.split(".").pop(),n.subType=k.unknown,n.type=E.Unknown,n):n.type>2?(n.extType=n.name.split(".").pop(),n.subType=n.type,n.type=E.CloudFile,n):n}(o),s=null!==(n=f.url)&&void 0!==n?n:function(e){switch(e.type){case"board":return"/boards/".concat(e.guid);case"mosheet":return"/sheets/".concat(e.guid);case"form":return"/forms/".concat(e.guid);case"mindmap":return"/mindmaps/".concat(e.guid);case"modoc":return"/docx/".concat(e.guid);case"newdoc":return"/docs/".concat(e.guid);case"presentation":return"/presentation/".concat(e.guid);case"table":return"/tables/".concat(e.guid)}return"/"}(f);null==l||l({guid:i,title:null!==(t=o.name)&&void 0!==t?t:"",type:r,subType:f.extType,link:s})};return"file-header-menus"===r?F(e,f):U(e,f)},U=function(e,n){var r,o=e.files;if(1!==o.length)return[];var i=o[0];return!i||!i.role||"unknown"===i.role||i.isDelete||i.kind&&!["ShimoFile","Folder","CloudFile"].includes(i.kind)?[]:[{operation:"insert",option:{insertAfter:"create-shortcut",insertBefore:"star"},menu:"right-menus",data:{key:"add-knowledge-base",title:(null===(r=window.s18n)||void 0===r?void 0:r.call(window,"添加到知识库"))||"添加到知识库",onPress:n,icon:t.default.createElement(I,null)}}]},F=function(e,n){var t;return 1!==e.files.length?[]:[{operation:"insert",option:{insertAfter:"FILE_MENU_CREATE_SHORTCUT",insertBefore:"star"},menu:"file-header-menus",data:{key:"add-knowledge-base",title:(null===(t=window.s18n)||void 0===t?void 0:t.call(window,"添加到知识库"))||"添加到知识库",onPress:n}}]};!function(){var e=H();e.isDefined("MenuExtension")||e.inject("MenuExtension",A)}()}(React);
</script></body></html>