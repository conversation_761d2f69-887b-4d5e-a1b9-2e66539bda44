#!/usr/bin/env python3
"""
通过API测试答案文字替换功能
"""

import asyncio
import aiohttp
import json
import sys
import os

async def test_api_answer_replacement():
    """通过API测试答案文字替换功能"""
    
    print("🔍 通过API测试答案文字替换功能...")
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    # 测试内容
    test_content = """
# API测试：答案文字替换

## 第一题

计算：2 + 3 = ?

/
这是一个简单的加法题目。

2 + 3 = 5

答案是5。
/

## 第二题

小明有8个苹果，吃了3个，还剩几个？

/
这是一个减法应用题。

**计算过程：**
8 - 3 = 5

**答案：** 5个苹果

所以最终答案是5个苹果。
/

## 第三题

一个正方形的边长是4cm，求面积。

/
正方形面积公式：面积 = 边长 × 边长

**计算：**
面积 = 4 × 4 = 16平方厘米

**答案：** 16平方厘米

这就是正确答案。
/
"""
    
    # 请求数据
    request_data = {
        "content": test_content,
        "layout_config": {
            "page_format": "A4",
            "margin_top": 2.0,
            "margin_bottom": 2.0,
            "margin_left": 2.0,
            "margin_right": 2.0,
            "font_size": 12,
            "line_height": 1.5,
            "paragraph_spacing": 12,
            "indent_first_line": True
        },
        "filename": "api_answer_replacement_test.pdf"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📡 发送API请求...")
            
            # 发送PDF生成请求
            async with session.post(
                f"{base_url}/api/pdf/generate",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API请求成功")
                    print(f"   📄 PDF URL: {result.get('pdf_url')}")
                    print(f"   📊 文件大小: {result.get('file_size')} 字节")
                    print(f"   📑 页数: {result.get('page_count')}")
                    print(f"   ⏱️ 生成时间: {result.get('generation_time'):.2f} 秒")
                    
                    # 尝试下载PDF文件
                    pdf_filename = result.get('pdf_url', '').split('/')[-1]
                    if pdf_filename:
                        print(f"\n📥 尝试下载PDF文件: {pdf_filename}")
                        
                        async with session.get(f"{base_url}/api/pdf/download/{pdf_filename}") as download_response:
                            if download_response.status == 200:
                                # 保存文件到本地
                                local_path = f"api_test_{pdf_filename}"
                                with open(local_path, 'wb') as f:
                                    f.write(await download_response.read())
                                
                                print(f"✅ PDF文件下载成功: {local_path}")
                                
                                # 自动打开PDF
                                import subprocess
                                import platform
                                system = platform.system()
                                if system == "Darwin":  # macOS
                                    subprocess.run(["open", local_path])
                                    print("🔍 已自动打开PDF文件")
                                    
                            else:
                                print(f"❌ PDF文件下载失败: {download_response.status}")
                    
                else:
                    error_text = await response.text()
                    print(f"❌ API请求失败: {response.status}")
                    print(f"   错误信息: {error_text}")
                    
    except aiohttp.ClientConnectorError:
        print("❌ 无法连接到API服务器")
        print("   请确保后端服务正在运行 (python -m uvicorn app.main:app --reload)")
        print("   或者运行: cd backend && python -m uvicorn app.main:app --reload")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

async def check_server_status():
    """检查服务器状态"""
    print("🔍 检查API服务器状态...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/docs") as response:
                if response.status == 200:
                    print("✅ API服务器正在运行")
                    return True
                else:
                    print(f"❌ API服务器响应异常: {response.status}")
                    return False
    except:
        print("❌ API服务器未运行")
        print("   请启动后端服务:")
        print("   cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return False

async def main():
    """主函数"""
    print("🚀 开始API答案替换功能测试\n")
    
    # 检查服务器状态
    server_running = await check_server_status()
    
    if server_running:
        print()
        await test_api_answer_replacement()
    else:
        print("\n💡 启动建议:")
        print("1. 打开终端")
        print("2. 进入项目目录")
        print("3. 运行: cd backend")
        print("4. 运行: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        print("5. 等待服务启动后重新运行此测试")

if __name__ == "__main__":
    asyncio.run(main())
