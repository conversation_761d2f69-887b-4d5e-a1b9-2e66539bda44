#!/usr/bin/env python3
"""
创建测试图片用于验证答案框中的图片显示功能
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images():
    """创建测试图片"""
    
    # 确保目录存在
    os.makedirs('test_images', exist_ok=True)
    
    # 创建一个简单的数学图表
    def create_math_diagram():
        # 创建图片
        width, height = 300, 200
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制坐标轴
        # X轴
        draw.line([(50, height-50), (width-50, height-50)], fill='black', width=2)
        # Y轴
        draw.line([(50, 50), (50, height-50)], fill='black', width=2)
        
        # 绘制一个简单的函数图像 y = x^2
        points = []
        for x in range(-5, 6):
            screen_x = 50 + (x + 5) * 20
            screen_y = height - 50 - (x * x) * 2
            if screen_y >= 50:  # 确保在图像范围内
                points.append((screen_x, screen_y))
        
        if len(points) > 1:
            draw.line(points, fill='blue', width=2)
        
        # 添加标题
        try:
            # 尝试使用默认字体
            font = ImageFont.load_default()
            draw.text((width//2 - 50, 20), "y = x²", fill='black', font=font)
        except:
            draw.text((width//2 - 20, 20), "y = x^2", fill='black')
        
        # 保存图片
        img.save('test_images/math_diagram.png')
        print("✅ 创建数学图表: test_images/math_diagram.png")
    
    # 创建一个几何图形
    def create_geometry_shape():
        # 创建图片
        width, height = 250, 200
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        # 绘制一个长方形
        rect_x1, rect_y1 = 50, 50
        rect_x2, rect_y2 = 200, 120
        draw.rectangle([(rect_x1, rect_y1), (rect_x2, rect_y2)], outline='blue', width=2)
        
        # 添加尺寸标注
        try:
            font = ImageFont.load_default()
            # 长度标注
            draw.text((rect_x1 + 50, rect_y2 + 10), "8cm", fill='black', font=font)
            # 宽度标注
            draw.text((rect_x2 + 10, rect_y1 + 20), "5cm", fill='black', font=font)
        except:
            draw.text((rect_x1 + 50, rect_y2 + 10), "8cm", fill='black')
            draw.text((rect_x2 + 10, rect_y1 + 20), "5cm", fill='black')
        
        # 保存图片
        img.save('test_images/rectangle.png')
        print("✅ 创建几何图形: test_images/rectangle.png")
    
    # 创建一个计算步骤图
    def create_calculation_steps():
        # 创建图片
        width, height = 280, 150
        img = Image.new('RGB', (width, height), '#f0f0f0')
        draw = ImageDraw.Draw(img)
        
        # 绘制边框
        draw.rectangle([(10, 10), (width-10, height-10)], outline='#333333', width=2)
        
        # 添加计算步骤文字
        try:
            font = ImageFont.load_default()
            steps = [
                "Step 1: 8 + 4 = 12",
                "Step 2: 12 × 3 = 36", 
                "Step 3: 6 ÷ 2 = 3",
                "Step 4: 36 - 3 = 33"
            ]
            
            for i, step in enumerate(steps):
                y_pos = 25 + i * 25
                draw.text((20, y_pos), step, fill='black', font=font)
        except:
            # 简化版本
            draw.text((20, 30), "Calculation Steps", fill='black')
            draw.text((20, 60), "(8+4)×3-6÷2=33", fill='black')
        
        # 保存图片
        img.save('test_images/calculation_steps.png')
        print("✅ 创建计算步骤图: test_images/calculation_steps.png")
    
    # 创建所有测试图片
    create_math_diagram()
    create_geometry_shape()
    create_calculation_steps()
    
    print("🎉 所有测试图片创建完成！")

if __name__ == "__main__":
    create_test_images()
