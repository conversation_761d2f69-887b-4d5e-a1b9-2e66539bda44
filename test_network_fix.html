<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络修复测试 - 答案替换功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 网络修复测试 - 答案替换功能</h1>
        
        <div class="test-section">
            <h3>📡 服务器状态检查</h3>
            <button onclick="checkServer()">检查后端服务</button>
            <div id="serverStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 答案替换功能测试</h3>
            <p>测试内容（包含"答案"文字）：</p>
            <textarea id="testContent"># 答案替换测试

## 测试题

计算：1 + 1 = ?

/
这是最简单的加法题目。

答案是2。

这里的"答案"应该显示为橙色标签图片。
/</textarea>
            
            <button onclick="testAnswerReplacement()">测试答案替换</button>
            <div id="testStatus"></div>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>💡 说明</h3>
            <ul>
                <li><strong>网络错误已修复：</strong> 后端服务现在正常运行在 http://localhost:8000</li>
                <li><strong>答案替换功能：</strong> 答案框中的"答案"文字会被替换为橙色标签图片</li>
                <li><strong>检查方法：</strong> 生成PDF后，查看"答案"是否显示为图片</li>
                <li><strong>路径修复：</strong> 已修复答案图片文件的路径查找问题</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function showStatus(elementId, message, type = 'info') {
            const statusDiv = document.getElementById(elementId);
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        function showResult(elementId, content) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = `<div class="result">${content}</div>`;
        }
        
        async function checkServer() {
            showStatus('serverStatus', '检查服务器状态...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/docs`);
                if (response.ok) {
                    showStatus('serverStatus', '✅ 后端服务运行正常！', 'success');
                } else {
                    showStatus('serverStatus', '❌ 服务器响应异常', 'error');
                }
            } catch (error) {
                showStatus('serverStatus', '❌ 无法连接到服务器', 'error');
            }
        }
        
        async function testAnswerReplacement() {
            const content = document.getElementById('testContent').value.trim();
            
            if (!content) {
                showStatus('testStatus', '请输入测试内容', 'error');
                return;
            }
            
            showStatus('testStatus', '正在生成PDF测试...', 'info');
            
            const requestData = {
                content: content,
                layout_config: {
                    page_format: "A4",
                    margin_top: 2.0,
                    margin_bottom: 2.0,
                    margin_left: 2.0,
                    margin_right: 2.0,
                    font_size: 12,
                    line_height: 1.5,
                    paragraph_spacing: 12,
                    indent_first_line: true
                },
                filename: "network_fix_answer_test.pdf"
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/pdf/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('testStatus', '✅ PDF生成成功！网络问题已解决！', 'success');
                    
                    // 创建下载链接
                    const downloadUrl = `${API_BASE}${result.pdf_url}`;
                    showResult('testResult', `
                        <h4>🎉 测试成功！</h4>
                        <p><strong>文件大小：</strong> ${result.file_size} 字节</p>
                        <p><strong>页数：</strong> ${result.page_count}</p>
                        <p><strong>生成时间：</strong> ${result.generation_time.toFixed(2)} 秒</p>
                        <p><a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📥 点击下载PDF文件</a></p>
                        <div style="margin-top: 15px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                            <strong>🔍 检查要点：</strong><br>
                            1. 打开下载的PDF文件<br>
                            2. 查找答案框（圆角矩形背景）<br>
                            3. 检查"答案"是否显示为橙色标签图片<br>
                            4. 确认其他文字正常显示<br><br>
                            <strong>✅ 如果看到橙色"答案"标签，说明功能完全正常！</strong>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    showStatus('testStatus', '❌ PDF生成失败', 'error');
                    showResult('testResult', `<h4>错误信息：</h4><pre>${errorText}</pre>`);
                }
                
            } catch (error) {
                showStatus('testStatus', '❌ 网络请求失败', 'error');
                showResult('testResult', `<h4>错误详情：</h4><pre>${error.message}</pre>`);
            }
        }
        
        // 页面加载时自动检查服务器状态
        window.onload = function() {
            checkServer();
        };
    </script>
</body>
</html>
