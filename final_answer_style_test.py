#!/usr/bin/env python3
"""
最终"答案"文字样式替换测试
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_answer_style_test():
    """最终"答案"文字样式替换测试"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容 - 与你的截图内容完全一致
    test_content = """
# "答案"文字样式替换功能测试

## 第一题：基础测试

这是一个计算题。

/
这是一个计算题。

答案：42

这道题的答案很容易得出。
/

## 第二题：多种格式测试

/
**第一小题答案：** A
**第二小题答案：** B  
**第三小题答案：** C

所有答案都已标注完毕。
/

## 第三题：混合内容测试

/
这道题需要分步计算：

1. 第一步计算
2. 第二步计算  
3. 得出答案

**最终答案：** 42

答案解析：通过三个步骤的计算，我们得到了正确的答案。（（答案的准确性很重要））
/

## 第四题：复杂文本测试

/
根据题目要求：

**计算过程：**
- 第一步：基础计算
- 第二步：应用公式
- 第三步：验证答案

**结果：** 最终答案是正确的

这个答案经过了多重验证，确保准确性。
/

## 验证说明

/
通过以上测试，我们验证了"答案"文字样式替换功能：

✅ **基础替换：** 单个"答案"文字正确替换为橙色粗体标记
✅ **多次替换：** 同一段落中多个"答案"都被正确处理
✅ **格式兼容：** 与粗体、特殊标记等格式完美兼容
✅ **视觉效果：** 橙色【答案】标记醒目且专业

**最终答案：** 功能测试全部通过！

所有的答案标记都应该显示为橙色粗体的【答案】样式。
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终'答案'文字样式替换测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_answer_style_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎯 验证要点：")
        print("   - 检查所有'答案'文字是否被替换为橙色粗体【答案】")
        print("   - 验证样式与文字的融合效果")
        print("   - 确认与其他格式的兼容性")
        print("   - 测试在不同上下文中的显示效果")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_answer_style_test())
