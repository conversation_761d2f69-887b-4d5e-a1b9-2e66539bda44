#!/usr/bin/env python3
"""
测试答案图片突出到答案框外面的效果
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_outside_box():
    """测试答案图片突出到答案框外面的效果"""
    
    print("🔍 测试答案图片突出到答案框外面的效果...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 答案图片突出显示测试

## 突出效果说明

计算：7 + 8 = ?

/
这是一个加法题目。

答案是15。

现在的"答案"图片应该突出到答案框的左边外面，比答案框本身更靠左。
/

## 视觉效果对比

/
**位置变化历程：**

1. **第一版：** 答案图片居中显示在答案框内
2. **第二版：** 答案图片左对齐到内容区域
3. **第三版：** 答案图片贴住答案框边框
4. **第四版：** 答案图片突出到答案框外面 ← 当前版本

**当前效果特点：**
- 答案图片位置：X = -10px（负坐标）
- 视觉效果：图片突出到答案框左边外面
- 突出距离：10像素
- 更加醒目和突出

这样的答案图片布局让答案更加显眼，形成了独特的视觉标识。
/

## 多个答案测试

第一题：9 × 6 = ?

/
乘法运算题目。

计算过程：9 × 6 = 54

答案：54
/

第二题：100 ÷ 4 = ?

/
除法运算题目。

计算过程：100 ÷ 4 = 25

答案：25
/

第三题：(12 + 8) ÷ 4 = ?

/
混合运算题目。

**第一步：** 计算括号
12 + 8 = 20

**第二步：** 计算除法
20 ÷ 4 = 5

**最终答案：** 5
/

## 复杂布局测试

综合应用题：小明买了3支笔，每支5元，又买了2本书，每本12元，一共花了多少钱？

/
这是一个综合应用题，需要分步计算。

**已知条件：**
- 笔：3支，每支5元
- 书：2本，每本12元

**计算步骤：**

**第一步：** 计算笔的总价
3 × 5 = 15元

**第二步：** 计算书的总价
2 × 12 = 24元

**第三步：** 计算总花费
15 + 24 = 39元

**最终答案：** 39元

所有的"答案"图片都应该突出到答案框外面，形成统一的视觉效果。

这种布局让答案部分更加突出，便于快速识别和查找。
/

## 边界测试

/
**技术实现说明：**

- 图片X坐标：-10px（负值）
- 突出距离：10像素
- 相对位置：答案框左边外面
- 视觉效果：图片"悬浮"在答案框外

**注意事项：**
- 答案图片可能会与页面左边距重叠
- 需要确保页面左边距足够容纳突出的图片
- 这种设计让答案更加醒目

**答案图片突出效果测试完成。**
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=3.0,  # 增加左边距以容纳突出的答案图片
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成答案突出显示测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_outside_box.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF中的答案图片突出效果:")
                print("1. ✅ 答案图片应该突出到答案框的左边外面")
                print("2. ✅ 答案图片比答案框本身更靠左")
                print("3. ✅ 答案图片使用负坐标(-10px)")
                print("4. ✅ 所有答案图片都保持这种突出效果")
                print("5. ✅ 答案图片更加醒目和突出")
                
                print("\n突出效果变化:")
                print("- 🔄 第一版：答案图片居中显示")
                print("- 📍 第二版：答案图片左对齐到内容区域")
                print("- 🎯 第三版：答案图片贴住边框")
                print("- ✅ 第四版：答案图片突出到答案框外面")
                
                print("\n技术实现:")
                print("- 修改前：img_x = self.border_width")
                print("- 修改后：img_x = -10 (负坐标突出)")
                print("- 突出距离：10像素")
                print("- 左边距：增加到3.0cm以容纳突出图片")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_outside_box())
