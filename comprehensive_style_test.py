#!/usr/bin/env python3
"""
综合样式测试 - 展示答案及解析样式与其他样式的组合使用
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_comprehensive_styles():
    """测试所有样式的综合使用"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建综合测试内容
    test_content = """
# 小学数学综合练习

## 基础运算

1. 计算下列表达式：8 + 6 ÷ 2 = ?

/这道题考查运算顺序。根据运算法则，除法优先于加法，所以先算6÷2=3，再算8+3=11。答案是11。（（这是一个关于运算顺序的重要知识点））/

2. 如果一个长方形的长是8cm，宽是5cm，求它的周长。

/长方形周长公式：周长 = 2×(长+宽)。代入数值：周长 = 2×(8+5) = 2×13 = 26cm。（（周长是指图形边界的总长度））/

3. 小明有20个苹果，吃掉了其中的1/4，还剩多少个？

/首先计算吃掉的苹果数：20 × 1/4 = 5个。然后计算剩余的苹果：20 - 5 = 15个。或者直接计算：20 × (1 - 1/4) = 20 × 3/4 = 15个。/

## 应用题练习

### 第一组：时间问题

小红从家到学校需要15分钟，如果她早上7:30从家出发，几点能到学校？

/这是一个时间加法问题。7:30 + 15分钟 = 7:45。所以小红7:45能到学校。（（时间计算要注意分钟满60进位到小时））/

### 第二组：购物问题

妈妈买了3支铅笔，每支2元，又买了2个橡皮，每个1.5元，一共花了多少钱？

/分步计算：
1. 铅笔总价：3 × 2 = 6元
2. 橡皮总价：2 × 1.5 = 3元  
3. 总花费：6 + 3 = 9元

答案是9元。这道题考查的是分步计算和小数乘法。/

## 几何图形

### 面积计算

1. 正方形边长6cm，面积是多少？

/正方形面积 = 边长 × 边长 = 6 × 6 = 36平方厘米。（（正方形是四边相等的特殊长方形））/

2. 圆形半径3cm，面积约是多少？（π取3.14）

/圆形面积公式：S = π × r²
代入数值：S = 3.14 × 3² = 3.14 × 9 = 28.26平方厘米
答案约是28.26平方厘米。/

## 总结

通过以上练习，我们复习了：
- **基础运算**：加减乘除和运算顺序
- **分数计算**：分数与整数的乘法
- **时间计算**：时间的加减运算  
- **几何图形**：正方形、长方形、圆形的面积计算

/学习数学需要多练习，掌握基本公式和运算法则。每个知识点都要理解原理，不能只记结果。（（数学是一门需要逻辑思维的学科，要培养分析问题和解决问题的能力））/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成综合样式测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="comprehensive_style_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("📋 测试内容包括：")
        print("   - 一级标题（带背景图片）")
        print("   - 编号列表（带圆形背景）") 
        print("   - 答案及解析框（圆角矩形背景）")
        print("   - 双括号文本（橙色楷体）")
        print("   - 普通段落和各级标题")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_comprehensive_styles())
