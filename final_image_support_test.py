#!/usr/bin/env python3
"""
最终图片支持测试 - 展示答案框中图片显示的完整功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_image_support_test():
    """最终图片支持测试"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建综合测试内容
    test_content = """
# 数学综合练习册（图文版）

## 第一章：函数与图像

### 1.1 二次函数

1. 观察下面的函数图像，说明这是什么函数，并写出其特点。

/这是一个二次函数 y = x² 的图像。

![数学图表](test_images/math_diagram.png)

**函数特点：**
1. **开口方向：** 开口向上
2. **对称轴：** y轴（x = 0）
3. **顶点：** 原点 (0, 0)
4. **最值：** 最小值为 0

**函数性质：**
- 当 x > 0 时，函数单调递增
- 当 x < 0 时，函数单调递减
- 函数值域为 [0, +∞)

这是最基本的二次函数形式。（（二次函数是中学数学的重要内容））/

## 第二章：几何计算

### 2.1 长方形面积与周长

2. 根据下图，计算长方形的面积和周长。

/根据图中给出的尺寸信息：

![长方形](test_images/rectangle.png)

**已知条件：**
- 长 = 8cm
- 宽 = 5cm

**面积计算：**
面积 = 长 × 宽 = 8 × 5 = 40平方厘米

**周长计算：**
周长 = 2 × (长 + 宽) = 2 × (8 + 5) = 26厘米

**答案总结：**
- 面积：40平方厘米  
- 周长：26厘米

这道题考查学生对长方形基本公式的掌握和应用能力。/

## 第三章：混合运算

### 3.1 四则混合运算

3. 计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/这道题需要严格按照运算顺序进行计算：

![计算步骤](test_images/calculation_steps.png)

**运算顺序：** 括号 → 乘除 → 加减

**详细步骤：**

**第一步：** 计算括号内容
(8 + 4) = 12

**第二步：** 从左到右计算乘除法
- 12 × 3 = 36
- 6 ÷ 2 = 3

**第三步：** 计算减法
36 - 3 = 33

**最终答案：** 33

运算顺序是数学计算的基础，必须严格遵守。（（记住口诀：先括号，后乘除，最后加减））/

### 3.2 逆向思维题

4. 如果一个正方形的面积是36平方厘米，求它的边长和周长。

/这是一个从面积求边长的逆向思维题。

**已知：** 正方形面积 = 36平方厘米

**求边长：**
∵ 正方形面积 = 边长²
∴ 边长² = 36
∴ 边长 = √36 = 6厘米

**求周长：**
正方形周长 = 4 × 边长 = 4 × 6 = 24厘米

![长方形](test_images/rectangle.png)

虽然上图是长方形，但如果长和宽都等于6厘米，就变成了我们要求的正方形。

**验证：** 6² = 36 ✓

**答案：**
- 边长：6厘米
- 周长：24厘米/

## 第四章：应用题

### 4.1 购物计算

5. 小明买文具，买了2支钢笔（每支15元）和3个笔记本（每个8元），一共花了多少钱？

/这是一个分步计算的应用题。

**第一步：** 计算钢笔总价
钢笔总价 = 2 × 15 = 30元

**第二步：** 计算笔记本总价  
笔记本总价 = 3 × 8 = 24元

**第三步：** 计算总花费
总花费 = 30 + 24 = 54元

**答案：** 小明一共花了54元。

这道题考查学生的分步计算能力和实际应用能力。/

### 4.2 图文对比测试

6. 简单计算：25 ÷ 5 + 3 × 2 = ?

/这是一个纯文字解析，用来对比图文混合的效果。

**计算过程：**
1. 先算除法：25 ÷ 5 = 5
2. 再算乘法：3 × 2 = 6
3. 最后算加法：5 + 6 = 11

**答案：** 11

对比可以看出，图文混合的解析更加直观和生动。/

## 总结

/通过本次综合测试，我们验证了答案及解析框的完整功能：

![数学图表](test_images/math_diagram.png)

**功能验证清单：**
✅ **文本内容：** 支持多段落、格式化文本
✅ **图片显示：** 支持本地图片嵌入
✅ **图文混合：** 文本和图片可以自由组合
✅ **自动布局：** 图片居中对齐，高度自适应
✅ **格式支持：** 粗体、斜体、特殊标记等
✅ **样式统一：** 圆角矩形背景，橙色边框

**技术特点：**
- 图片自动缩放适应框宽度
- 图片在答案框中居中显示
- 支持多种图片格式（PNG、JPG等）
- 文本和图片混合排列
- 保持整体视觉风格统一

这个功能为教育材料的制作提供了强大的支持，让答案解析更加直观和易懂。（（图文并茂的教学材料更有助于学生理解））/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终图片支持测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_image_support_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎉 最终图片支持测试完成！")
        print("📋 包含的完整功能：")
        print("   ✅ 一级标题（带背景图片）")
        print("   ✅ 二级和三级标题")
        print("   ✅ 编号列表（带圆形背景）")
        print("   ✅ 答案及解析框（文本内容）")
        print("   ✅ 答案及解析框（图片显示）")
        print("   ✅ 答案及解析框（图文混合）")
        print("   ✅ 双括号文本（橙色楷体）")
        print("   ✅ 粗体和格式化文本")
        print("   ✅ 图片自动缩放和居中")
        print("   ✅ 自动换行和高度调整")
        print("   ✅ 所有样式的完美融合")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_image_support_test())
