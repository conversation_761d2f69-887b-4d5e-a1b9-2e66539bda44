#!/usr/bin/env python3
"""
最终综合测试 - 展示所有样式功能的完整使用
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_comprehensive_test():
    """最终综合测试所有样式功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建综合测试内容
    test_content = """
# 小学数学综合练习册

## 第一章：基础运算

### 1.1 加减法练习

1. 计算：25 + 37 = ?

/答案是62。这是一个简单的两位数加法。/

2. 计算：83 - 29 = ?

/
这是一个需要借位的减法题目。

**计算过程：**
- 个位：3 - 9 不够减，需要从十位借1
- 个位变成：13 - 9 = 4
- 十位：8 - 1 - 2 = 5

**答案：** 54

这道题考查学生对借位减法的掌握。（（借位是减法运算中的重要技巧））
/

### 1.2 乘除法练习

3. 计算：6 × 8 = ?

/答案是48。这是九九乘法表中的基础题目。/

4. 计算：144 ÷ 12 = ?

/
这是一个整除的除法题目。

**方法一：直接计算**
144 ÷ 12 = 12

**方法二：分解计算**
144 = 12 × 12，所以 144 ÷ 12 = 12

**验证：** 12 × 12 = 144 ✓

答案是12。这道题可以通过背诵乘法表或者长除法来解决。
/

## 第二章：应用题

### 2.1 购物问题

小明去商店买文具，买了3支铅笔（每支2元）和2块橡皮（每块1.5元），一共需要多少钱？

/
这是一个多步骤的应用题，需要分别计算不同物品的总价。

**第一步：计算铅笔总价**
铅笔总价 = 3 × 2 = 6元

**第二步：计算橡皮总价**
橡皮总价 = 2 × 1.5 = 3元

**第三步：计算总花费**
总花费 = 6 + 3 = 9元

**答案：** 小明一共需要9元。

这道题考查学生的分步计算能力和小数乘法运算。
/

### 2.2 时间问题

如果现在是上午9:15，再过2小时30分钟是几点？

/
这是一个时间加法问题。

**计算过程：**
起始时间：9:15
增加时间：2小时30分钟

**分步计算：**
1. 先加小时：9:15 + 2小时 = 11:15
2. 再加分钟：11:15 + 30分钟 = 11:45

**答案：** 11:45（上午11点45分）

**注意事项：** 时间计算要注意分钟满60进位到小时。（（时间单位的换算是日常生活中的重要技能））
/

## 第三章：几何图形

### 3.1 面积计算

5. 一个长方形的长是12cm，宽是8cm，求它的面积。

/
长方形面积公式：面积 = 长 × 宽

代入数值：面积 = 12 × 8 = 96平方厘米

答案是96平方厘米。
/

6. 一个正方形的周长是20cm，求它的面积。

/
这道题需要先求出边长，再计算面积。

**第一步：求边长**
正方形周长 = 4 × 边长
20 = 4 × 边长
边长 = 20 ÷ 4 = 5cm

**第二步：求面积**
正方形面积 = 边长 × 边长 = 5 × 5 = 25平方厘米

**答案：** 25平方厘米

这道题考查学生对正方形周长和面积公式的综合运用。
/

### 3.2 体积计算

7. 一个正方体的棱长是4cm，求它的体积。

/
正方体体积公式：体积 = 棱长³

代入数值：体积 = 4³ = 4 × 4 × 4 = 64立方厘米

答案是64立方厘米。（（立方厘米是体积的常用单位））
/

## 第四章：分数与小数

### 4.1 分数计算

8. 计算：1/2 + 1/4 = ?

/
这是一个分数加法题目，需要通分。

**通分过程：**
1/2 = 2/4
1/4 = 1/4

**相加：**
2/4 + 1/4 = 3/4

**答案：** 3/4

分数加法的关键是找到公分母进行通分。
/

### 4.2 小数计算

9. 计算：3.5 + 2.8 = ?

/
小数加法要注意小数点对齐。

  3.5
+ 2.8
-----
  6.3

答案是6.3。
/

## 总结

/
通过这套综合练习，我们复习了小学数学的主要内容：

**基础运算：**
- 加减法（包括借位）
- 乘除法（包括九九乘法表）

**应用题：**
- 购物计算问题
- 时间计算问题

**几何图形：**
- 长方形和正方形的面积
- 正方体的体积

**分数与小数：**
- 分数的通分和加法
- 小数的加法运算

这些都是小学数学的核心内容，需要通过大量练习来熟练掌握。（（数学学习需要循序渐进，打好基础很重要））
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终综合测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_comprehensive_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎉 最终综合测试完成！")
        print("📋 包含的所有样式功能：")
        print("   ✅ 一级标题（带背景图片）")
        print("   ✅ 二级和三级标题")
        print("   ✅ 编号列表（带圆形背景）")
        print("   ✅ 答案及解析框（单行格式）")
        print("   ✅ 答案及解析框（多段落格式）")
        print("   ✅ 双括号文本（橙色楷体）")
        print("   ✅ 粗体和格式化文本")
        print("   ✅ 自动换行和高度调整")
        print("   ✅ 所有样式的混合使用")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_comprehensive_test())
