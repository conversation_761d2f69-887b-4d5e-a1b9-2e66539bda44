# 答案图片尺寸说明

## 📐 答案图片基本信息

### 原始图片尺寸
- **宽度：** 209 像素
- **高度：** 101 像素  
- **宽高比：** 2.07:1
- **文件大小：** 3,513 字节

### PDF中显示尺寸（缩小50%后）
- **显示宽度：** 40.0 像素
- **显示高度：** 19.3 像素
- **缩放比例：** 0.19 (缩小到原始尺寸的19%)

## 🎯 尺寸计算规则

### 宽度计算（缩小50%后）
```python
target_width = min(40, max_width * 0.1)  # 最大40像素或答案框宽度的10%（缩小50%）
```

### 高度计算
```python
scale_ratio = min(target_width / orig_width, 1.0)  # 不放大图片
new_width = orig_width * scale_ratio
new_height = orig_height * scale_ratio
```

### 具体数值（缩小50%后）
- **最大宽度限制：** 40像素
- **宽度比例限制：** 答案框宽度的10%
- **实际显示宽度：** 40像素 (因为40 < 400×0.1=40)
- **实际显示高度：** 19.3像素 (按比例计算)

## 📍 位置和对齐

### 水平位置
- **X坐标：** -10像素 (负值)
- **效果：** 突出到答案框左边外面10像素
- **对齐方式：** 比答案框本身更靠左

### 垂直位置
- **Y坐标：** 根据内容流动态计算
- **间距：** 图片下方5像素间距

## 🎨 图片版本

系统提供了三个版本的答案图片：

| 文件名 | 用途 | 尺寸 | 说明 |
|--------|------|------|------|
| `answer_label.png` | 标准版本 | 209×101px | 默认使用 |
| `answer_label_small.png` | 小尺寸版本 | 209×101px | 备选方案 |
| `answer_label_large.png` | 大尺寸版本 | 209×101px | 备选方案 |

**注意：** 目前所有版本的原始尺寸相同，系统会自动选择第一个找到的版本。

## 🔧 技术实现

### 代码位置
```python
# backend/app/services/pdf_service.py
def _create_answer_image(self, max_width: float) -> Optional[Image]:
    # 计算合适的尺寸（答案图片应该比较小）
    target_width = min(80, max_width * 0.2)  # 最大80像素或20%宽度
    scale_ratio = min(target_width / orig_width, 1.0)  # 不放大
    
    new_width = orig_width * scale_ratio
    new_height = orig_height * scale_ratio
```

### 绘制位置
```python
# 答案图片左对齐显示（突出到答案框外面）
img_x = -10  # 负值让图片突出到答案框左边外面10px
content_obj.drawOn(canvas, img_x, current_y)
```

## 📊 尺寸对比

### 相对于答案框
- **答案框典型宽度：** 400-500像素
- **答案图片宽度：** 80像素 (约16-20%的答案框宽度)
- **答案图片高度：** 38.7像素

### 相对于文字
- **文字字体大小：** 12pt (约16像素)
- **答案图片高度：** 38.7像素 (约2.4倍文字高度)
- **视觉比例：** 适中，不会过大或过小

## 🎯 设计考虑

### 尺寸选择原因
1. **80像素宽度：** 足够清晰显示"答案"文字，不会过大占用空间
2. **38.7像素高度：** 与文字行高协调，视觉平衡
3. **2.07宽高比：** 横向布局，符合中文"答案"二字的自然比例

### 位置选择原因
1. **-10像素X坐标：** 突出显示，增强视觉识别度
2. **左对齐：** 与文字阅读习惯一致
3. **5像素下间距：** 适当的视觉分隔

## 🔄 自定义尺寸

如果需要调整答案图片尺寸，可以修改以下参数：

### 修改最大宽度
```python
target_width = min(100, max_width * 0.25)  # 改为100像素或25%宽度
```

### 修改突出距离
```python
img_x = -15  # 改为突出15像素
```

### 修改间距
```python
current_y -= 8  # 改为8像素间距
```

## 📝 使用示例

在Markdown中使用答案图片：

```markdown
/
这是一个数学题的解析。

计算过程：5 + 3 = 8

答案是8。  ← 这里的"答案"会被替换为图片
/
```

生成的PDF中：
- "答案"文字被替换为80×38.7像素的图片
- 图片突出到答案框左边外面10像素
- 图片下方有5像素间距
