#!/usr/bin/env python3
"""
测试答案图片路径修复
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_fix():
    """测试答案图片路径修复"""
    
    print("🔍 测试答案图片路径修复...")
    
    # 1. 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 2. 检查答案图片文件
    print("\n检查答案图片文件:")
    answer_paths = [
        "answer_images/answer_label.png",
        "../answer_images/answer_label.png",
        os.path.join(os.path.dirname(__file__), "answer_images", "answer_label.png")
    ]
    
    for path in answer_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到: {path}")
        else:
            print(f"   ❌ 未找到: {path}")
    
    # 3. 测试PDF服务
    print("\n测试PDF服务...")
    pdf_service = PDFService()
    
    # 4. 创建简单测试内容
    test_content = """
# 答案图片修复测试

## 测试题

计算：1 + 1 = ?

/
这是最简单的测试。

答案是2。

这里应该显示答案图片。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_fix.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                print("\n请检查PDF中的答案框:")
                print("1. 是否有圆角矩形背景")
                print("2. '答案'是否显示为橙色标签图片")
                print("3. 其他文字是否正常显示")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_fix())
