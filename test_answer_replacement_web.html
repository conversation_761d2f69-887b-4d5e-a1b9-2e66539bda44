<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答案替换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .preset-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .preset-test {
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .preset-test:hover {
            background-color: #f8f9fa;
        }
        .preset-test h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .preset-test p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 答案文字替换功能测试</h1>
        
        <div class="test-section">
            <h3>📋 预设测试用例</h3>
            <p>点击下面的测试用例快速填入内容：</p>
            
            <div class="preset-tests">
                <div class="preset-test" onclick="loadPreset('simple')">
                    <h4>简单测试</h4>
                    <p>最基础的答案替换测试</p>
                </div>
                
                <div class="preset-test" onclick="loadPreset('multiple')">
                    <h4>多个答案</h4>
                    <p>一个答案框中包含多个"答案"</p>
                </div>
                
                <div class="preset-test" onclick="loadPreset('complex')">
                    <h4>复杂格式</h4>
                    <p>包含格式化文本和特殊标记</p>
                </div>
                
                <div class="preset-test" onclick="loadPreset('mixed')">
                    <h4>图文混合</h4>
                    <p>包含图片和答案替换</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试内容</h3>
            <p>在下面输入你的Markdown内容，确保包含 <code>/内容/</code> 格式的答案框：</p>
            <textarea id="content" placeholder="请输入包含答案框的Markdown内容...">
# 测试

## 第一题

计算：1 + 1 = ?

/
这是最简单的加法题目。

答案是2。
/</textarea>
        </div>
        
        <div class="test-section">
            <h3>⚙️ 操作</h3>
            <button onclick="generatePDF()">生成PDF测试</button>
            <button onclick="checkServer()">检查服务器状态</button>
            <button onclick="clearContent()">清空内容</button>
            
            <div id="status"></div>
            <div id="result"></div>
        </div>
        
        <div class="test-section">
            <h3>💡 使用说明</h3>
            <ul>
                <li><strong>确保后端服务运行：</strong> <code>cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000</code></li>
                <li><strong>答案框格式：</strong> 使用 <code>/内容/</code> 包围需要应用样式的文本</li>
                <li><strong>答案替换：</strong> 答案框中的"答案"文字会自动替换为橙色标签图片</li>
                <li><strong>检查结果：</strong> 生成的PDF中应该看到橙色的"答案"标签而不是文字</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 预设测试用例
        const presets = {
            simple: `# 简单测试

## 基础题目

计算：2 + 3 = ?

/
这是一个简单的加法题目。

答案是5。
/`,
            
            multiple: `# 多答案测试

## 选择题

下列哪些是偶数？A.2 B.3 C.4 D.5

/
偶数是能被2整除的数字。

A. 2是偶数，答案正确
B. 3不是偶数
C. 4是偶数，答案正确  
D. 5不是偶数

最终答案：A和C
/`,
            
            complex: `# 复杂格式测试

## 应用题

小明有15个苹果，吃了5个，还剩多少个？

/
这是一个减法应用题。

**计算过程：**
15 - 5 = 10

**答案：** 10个苹果

这道题考查基础减法运算。（（减法是重要的数学技能））

所以最终答案是10个苹果。
/`,
            
            mixed: `# 图文混合测试

## 几何题

计算长方形面积：

/
根据题目条件：

长 = 8cm，宽 = 5cm

**计算：**
面积 = 长 × 宽 = 8 × 5 = 40平方厘米

**答案：** 40平方厘米

这就是正确答案。
/`
        };
        
        function loadPreset(type) {
            document.getElementById('content').value = presets[type];
            showStatus('已加载预设测试用例：' + type, 'info');
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        function showResult(content) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result">${content}</div>`;
        }
        
        async function checkServer() {
            showStatus('检查服务器状态...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/docs`);
                if (response.ok) {
                    showStatus('✅ 服务器运行正常', 'success');
                } else {
                    showStatus('❌ 服务器响应异常', 'error');
                }
            } catch (error) {
                showStatus('❌ 无法连接到服务器，请确保后端服务正在运行', 'error');
                showResult(`
                    <h4>启动后端服务：</h4>
                    <pre>cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000</pre>
                `);
            }
        }
        
        async function generatePDF() {
            const content = document.getElementById('content').value.trim();
            
            if (!content) {
                showStatus('请输入测试内容', 'error');
                return;
            }
            
            showStatus('正在生成PDF...', 'info');
            
            const requestData = {
                content: content,
                layout_config: {
                    page_format: "A4",
                    margin_top: 2.0,
                    margin_bottom: 2.0,
                    margin_left: 2.0,
                    margin_right: 2.0,
                    font_size: 12,
                    line_height: 1.5,
                    paragraph_spacing: 12,
                    indent_first_line: true
                },
                filename: "web_test_answer_replacement.pdf"
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/pdf/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('✅ PDF生成成功！', 'success');
                    
                    // 创建下载链接
                    const downloadUrl = `${API_BASE}${result.pdf_url}`;
                    showResult(`
                        <h4>PDF生成成功！</h4>
                        <p><strong>文件大小：</strong> ${result.file_size} 字节</p>
                        <p><strong>页数：</strong> ${result.page_count}</p>
                        <p><strong>生成时间：</strong> ${result.generation_time.toFixed(2)} 秒</p>
                        <p><a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📥 点击下载PDF文件</a></p>
                        <p style="margin-top: 15px; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                            <strong>检查要点：</strong><br>
                            1. 打开PDF文件<br>
                            2. 查找答案框（圆角矩形背景）<br>
                            3. 检查"答案"是否显示为橙色标签图片<br>
                            4. 确认其他文字正常显示
                        </p>
                    `);
                } else {
                    const errorText = await response.text();
                    showStatus('❌ PDF生成失败', 'error');
                    showResult(`<h4>错误信息：</h4><pre>${errorText}</pre>`);
                }
                
            } catch (error) {
                showStatus('❌ 请求失败，请检查服务器连接', 'error');
                showResult(`<h4>错误详情：</h4><pre>${error.message}</pre>`);
            }
        }
        
        function clearContent() {
            document.getElementById('content').value = '';
            document.getElementById('status').innerHTML = '';
            document.getElementById('result').innerHTML = '';
        }
        
        // 页面加载时检查服务器状态
        window.onload = function() {
            checkServer();
        };
    </script>
</body>
</html>
