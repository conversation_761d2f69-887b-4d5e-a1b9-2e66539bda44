<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答案图片对齐测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .before {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .after {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📐 答案图片对齐测试</h1>
        
        <div class="test-section">
            <h3>🎯 对齐效果对比</h3>
            <div class="comparison">
                <div class="comparison-item before">
                    <h4>🔄 修改前</h4>
                    <p>答案图片居中显示</p>
                    <p>图片在答案框中央</p>
                </div>
                <div class="comparison-item after">
                    <h4>✅ 修改后</h4>
                    <p>答案图片贴住边框</p>
                    <p>比内容文字更靠左</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试内容</h3>
            <textarea id="testContent"># 答案对齐测试

## 测试题

计算：1 + 1 = ?

/
这是一个简单的加法题目。

答案是2。

这里的"答案"图片应该左对齐贴住答案框边缘，而不是居中显示。
/</textarea>
            
            <button onclick="testAlignment()">测试答案对齐</button>
            <div id="testStatus"></div>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 检查要点</h3>
            <ul>
                <li><strong>✅ 贴住边框：</strong> 答案图片应该贴住答案框的左边框</li>
                <li><strong>❌ 不居中：</strong> 答案图片不应该在答案框中央</li>
                <li><strong>📏 更靠左：</strong> 答案图片比内容文字更靠左</li>
                <li><strong>🔄 多个答案：</strong> 所有答案图片都保持这种边框对齐</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>💡 技术说明</h3>
            <p><strong>修改内容：</strong></p>
            <ul>
                <li>修改了 <code>AnswerAnalysisBox.draw()</code> 方法</li>
                <li>将图片X坐标从 <code>content_x + (content_width - img_width) / 2</code> 改为 <code>content_x</code></li>
                <li>减少了图片间距从10px改为5px</li>
                <li>确保答案图片与文字左对齐</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        function showResult(content) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="result">${content}</div>`;
        }
        
        async function testAlignment() {
            const content = document.getElementById('testContent').value.trim();
            
            if (!content) {
                showStatus('请输入测试内容', 'error');
                return;
            }
            
            showStatus('正在生成PDF测试...', 'info');
            
            const requestData = {
                content: content,
                layout_config: {
                    page_format: "A4",
                    margin_top: 2.0,
                    margin_bottom: 2.0,
                    margin_left: 2.0,
                    margin_right: 2.0,
                    font_size: 12,
                    line_height: 1.5,
                    paragraph_spacing: 12,
                    indent_first_line: true
                },
                filename: "answer_alignment_test.pdf"
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/pdf/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('✅ PDF生成成功！', 'success');
                    
                    const downloadUrl = `${API_BASE}${result.pdf_url}`;
                    showResult(`
                        <h4>🎉 对齐测试完成！</h4>
                        <p><strong>文件大小：</strong> ${result.file_size} 字节</p>
                        <p><strong>生成时间：</strong> ${result.generation_time.toFixed(2)} 秒</p>
                        <p><a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📥 点击下载PDF文件</a></p>
                        <div style="margin-top: 15px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                            <strong>🔍 检查答案图片对齐：</strong><br>
                            1. 打开下载的PDF文件<br>
                            2. 查找答案框（圆角矩形背景）<br>
                            3. 检查"答案"图片是否左对齐<br>
                            4. 确认图片贴住答案框左边缘<br>
                            5. 验证图片不再居中显示<br><br>
                            <strong>✅ 如果答案图片左对齐，说明修改成功！</strong>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    showStatus('❌ PDF生成失败', 'error');
                    showResult(`<h4>错误信息：</h4><pre>${errorText}</pre>`);
                }
                
            } catch (error) {
                showStatus('❌ 网络请求失败', 'error');
                showResult(`<h4>错误详情：</h4><pre>${error.message}</pre>`);
            }
        }
    </script>
</body>
</html>
