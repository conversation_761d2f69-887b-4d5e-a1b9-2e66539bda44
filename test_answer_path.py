#!/usr/bin/env python3
"""
测试答案图片路径查找
"""

import os

def test_answer_image_paths():
    """测试答案图片路径查找"""
    
    print("🔍 测试答案图片路径查找...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本文件位置: {__file__}")
    print(f"脚本目录: {os.path.dirname(__file__)}")
    
    # 模拟后端服务中的路径查找逻辑
    answer_image_paths = [
        "answer_images/answer_label.png",
        "answer_images/answer_label_small.png", 
        "answer_images/answer_label_large.png",
        "../answer_images/answer_label.png",
        "../answer_images/answer_label_small.png",
        "../answer_images/answer_label_large.png",
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label.png"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_small.png"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "answer_images", "answer_label_large.png"),
    ]
    
    print("\n检查答案图片路径:")
    found_path = None
    
    for i, path in enumerate(answer_image_paths):
        abs_path = os.path.abspath(path)
        exists = os.path.exists(path)
        print(f"   {i+1}. {path}")
        print(f"      绝对路径: {abs_path}")
        print(f"      存在: {'✅' if exists else '❌'}")
        
        if exists and not found_path:
            found_path = path
            print(f"      >>> 使用此路径")
        print()
    
    if found_path:
        print(f"✅ 找到答案图片: {found_path}")
        
        # 检查图片文件大小
        file_size = os.path.getsize(found_path)
        print(f"📄 文件大小: {file_size} 字节")
        
        return found_path
    else:
        print("❌ 未找到任何答案图片文件")
        return None

def test_from_backend_directory():
    """模拟从backend目录运行的情况"""
    print("\n" + "="*50)
    print("模拟从backend目录运行:")
    
    # 切换到backend目录
    original_cwd = os.getcwd()
    backend_dir = os.path.join(os.getcwd(), 'backend')
    
    if os.path.exists(backend_dir):
        os.chdir(backend_dir)
        print(f"切换到: {os.getcwd()}")
        
        # 重新测试路径
        test_answer_image_paths()
        
        # 恢复原目录
        os.chdir(original_cwd)
    else:
        print("backend目录不存在")

if __name__ == "__main__":
    # 从项目根目录测试
    test_answer_image_paths()
    
    # 从backend目录测试
    test_from_backend_directory()
