#!/usr/bin/env python3
"""
检查答案图片的实际尺寸
"""

import os
from PIL import Image as PILImage

def check_answer_image_sizes():
    """检查所有答案图片的尺寸"""
    
    print("🔍 检查答案图片尺寸...")
    
    # 答案图片路径
    answer_image_paths = [
        "answer_images/answer_label.png",
        "answer_images/answer_label_small.png", 
        "answer_images/answer_label_large.png"
    ]
    
    for image_path in answer_image_paths:
        if os.path.exists(image_path):
            try:
                with PILImage.open(image_path) as img:
                    width, height = img.size
                    file_size = os.path.getsize(image_path)
                    
                    print(f"\n📄 {image_path}")
                    print(f"   📐 原始尺寸: {width} × {height} 像素")
                    print(f"   📊 宽高比: {width/height:.2f}")
                    print(f"   💾 文件大小: {file_size} 字节")
                    
                    # 模拟PDF中的尺寸计算（缩小50%后）
                    max_width = 400  # 假设答案框最大宽度400像素
                    target_width = min(40, max_width * 0.1)  # 最大40像素或10%宽度（缩小50%）
                    scale_ratio = min(target_width / width, 1.0)  # 不放大
                    
                    new_width = width * scale_ratio
                    new_height = height * scale_ratio
                    
                    print(f"   🎯 PDF中显示尺寸: {new_width:.1f} × {new_height:.1f} 像素")
                    print(f"   📏 缩放比例: {scale_ratio:.2f}")
                    
            except Exception as e:
                print(f"❌ 无法读取图片 {image_path}: {e}")
        else:
            print(f"❌ 图片文件不存在: {image_path}")
    
    print("\n" + "="*50)
    print("📋 答案图片尺寸规则总结（缩小50%后）:")
    print("1. 最大宽度: min(40px, 答案框宽度 × 10%) [原来80px/20%]")
    print("2. 高度: 按原始宽高比自动计算")
    print("3. 缩放: 只缩小，不放大")
    print("4. 位置: X = -10px (突出到答案框外面)")
    print("5. 间距: 图片下方5px间距")
    print("6. 缩小比例: 50% (面积减少75%)")
    
    print("\n🎨 不同尺寸版本说明:")
    print("- answer_label.png: 标准版本")
    print("- answer_label_small.png: 小尺寸版本")
    print("- answer_label_large.png: 大尺寸版本")
    print("- 系统会自动选择第一个找到的版本")

if __name__ == "__main__":
    check_answer_image_sizes()
