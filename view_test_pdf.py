#!/usr/bin/env python3
"""
打开测试生成的PDF文件
"""

import os
import subprocess
import platform

def open_pdf(pdf_path):
    """根据操作系统打开PDF文件"""
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["open", pdf_path])
        elif system == "Windows":
            os.startfile(pdf_path)
        else:  # Linux
            subprocess.run(["xdg-open", pdf_path])
        
        print(f"✅ 已打开PDF文件: {pdf_path}")
        return True
    except Exception as e:
        print(f"❌ 打开PDF文件失败: {e}")
        return False

if __name__ == "__main__":
    pdf_path = "generated_pdfs/test_answer_analysis.pdf"
    open_pdf(pdf_path)
