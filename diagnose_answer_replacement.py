#!/usr/bin/env python3
"""
诊断"答案"文字替换功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def diagnose_answer_replacement():
    """诊断"答案"文字替换功能"""
    
    print("🔍 开始诊断'答案'文字替换功能...")
    
    # 1. 检查图片文件是否存在
    print("\n1. 检查答案图片文件：")
    answer_image_paths = [
        "answer_images/answer_label.png",
        "answer_images/answer_label_small.png",
        "answer_images/answer_label_large.png"
    ]
    
    for path in answer_image_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            print(f"   ✅ {path} (大小: {file_size} 字节)")
        else:
            print(f"   ❌ {path} (文件不存在)")
    
    # 2. 测试不同的使用场景
    print("\n2. 测试不同使用场景：")
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 包含各种可能的使用方式
    test_content = """
# 诊断测试文档

## 场景1：在答案框中使用（应该生效）

计算：1 + 1 = ?

/
这是在答案及解析框中的测试。

**答案：** 2

这里的答案应该显示为图片。
/

## 场景2：普通段落中使用（不会生效）

这是普通段落中的答案，不会被替换。

**答案：** 这里不会变成图片

## 场景3：答案框中的多种格式

/
**测试1：** 答案在粗体后面
**测试2：** 这里有答案在中间
**测试3：** 答案

答案解析：所有答案都应该是图片。
/

## 场景4：答案框中的复杂格式

/
这道题需要分步计算：

1. 第一步计算
2. 第二步计算  
3. 得出答案

**最终答案：** 42

（（答案的准确性很重要））
/

## 场景5：图文混合中的答案

/
根据计算得出结果：

![测试图片](test_images/math_diagram.png?size=small)

通过图表分析，我们得到答案是正确的。

**答案：** 结果验证通过
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("\n3. 生成诊断PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="diagnose_answer_replacement.pdf"
        )
        
        print(f"✅ 诊断PDF生成成功: {pdf_path}")
        
        # 检查文件
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开诊断PDF文件")
                
        print("\n📋 诊断结果说明：")
        print("   - 场景1（答案框中）：'答案'应该显示为橙色图片")
        print("   - 场景2（普通段落）：'答案'保持为普通文字")
        print("   - 场景3-5（答案框中）：所有'答案'都应该是图片")
        print("\n💡 如果场景1中的'答案'没有变成图片，请检查：")
        print("   1. 是否在 /内容/ 格式的答案框中使用")
        print("   2. 答案图片文件是否存在")
        print("   3. 是否有其他错误信息")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(diagnose_answer_replacement())
