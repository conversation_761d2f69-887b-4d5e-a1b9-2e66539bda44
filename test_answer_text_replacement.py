#!/usr/bin/env python3
"""
测试"答案"文字替换为图片的功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_text_replacement():
    """测试"答案"文字替换为图片的功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# "答案"文字替换图片功能测试

## 第一题：基础计算

计算：5 + 3 = ?

/
这是一个简单的加法题目。

**答案：** 8

这道题考查基本的加法运算能力。
/

## 第二题：乘法运算

计算：6 × 7 = ?

/
这是九九乘法表中的题目。

**答案：** 42

记住：6 × 7 = 42，这是一个常用的乘法结果。
/

## 第三题：应用题

小明有10个苹果，吃掉了3个，还剩多少个？

/
这是一个减法应用题。

**计算过程：** 10 - 3 = 7

**答案：** 7个苹果

答案解析：从总数中减去已经吃掉的数量，就是剩余的数量。
/

## 第四题：几何问题

一个正方形的边长是4cm，求它的面积。

/
正方形面积公式：面积 = 边长 × 边长

**计算：** 4 × 4 = 16

**答案：** 16平方厘米

答案说明：正方形的面积等于边长的平方。
/

## 第五题：分数计算

计算：1/2 + 1/4 = ?

/
分数加法需要通分。

**通分过程：**
1/2 = 2/4
1/4 = 1/4

**相加：** 2/4 + 1/4 = 3/4

**答案：** 3/4

答案提示：分数加法的关键是找到公分母。
/

## 第六题：混合运算

计算：(8 + 2) × 3 - 5 = ?

/
按照运算顺序进行计算：

**第一步：** 括号内的加法
8 + 2 = 10

**第二步：** 乘法
10 × 3 = 30

**第三步：** 减法
30 - 5 = 25

**答案：** 25

答案总结：混合运算要严格按照运算顺序进行。
/

## 第七题：文字题

如果今天是星期三，那么3天后是星期几？

/
从星期三开始数3天：

星期三 → 星期四 → 星期五 → 星期六

**答案：** 星期六

答案解释：从当前日期开始，依次向后数指定的天数。
/

## 第八题：比较大小

比较：0.5 和 1/2 哪个大？

/
将小数和分数转换为同一形式进行比较：

**方法一：** 将分数转为小数
1/2 = 0.5

**方法二：** 将小数转为分数
0.5 = 1/2

**答案：** 0.5 = 1/2，它们相等

答案说明：0.5和1/2表示的是同一个数值。
/

## 总结

/
通过以上测试，我们验证了"答案"文字自动替换为图片的功能：

1. **功能验证：** 所有包含"答案"文字的地方都应该显示为橙色图片标签
2. **显示效果：** 图片标签与文字自然融合，不影响阅读
3. **样式统一：** 所有答案标签使用统一的橙色圆角矩形样式
4. **兼容性：** 与其他格式化文本（粗体、特殊标记等）完美兼容

**答案：** 功能测试成功！

这个功能让教学材料中的答案部分更加醒目和专业。
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成'答案'文字替换图片测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_text_replacement.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🖼️ 测试内容包括：")
        print("   - 多个包含'答案'文字的段落")
        print("   - 不同上下文中的答案文字")
        print("   - 答案文字与其他格式的混合使用")
        print("   - 验证图片替换效果")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_text_replacement())
