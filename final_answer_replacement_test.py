#!/usr/bin/env python3
"""
最终"答案"文字替换功能测试
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_answer_replacement_test():
    """最终"答案"文字替换功能测试"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# "答案"文字替换图片功能验证

## 第一题：基础测试

计算：3 + 5 = ?

/
这是一个简单的加法题目。

**答案：** 8

这道题的答案很容易计算出来。
/

## 第二题：多个答案测试

/
**第一小题答案：** A
**第二小题答案：** B  
**第三小题答案：** C

所有答案都已标注完毕。
/

## 第三题：混合格式测试

/
这道题有多种解法，但答案是唯一的。

**方法一：** 直接计算得出答案
**方法二：** 通过公式推导得出答案

无论用哪种方法，最终答案都是一样的。（（答案的准确性很重要））
/

## 第四题：图文混合测试

/
根据下图计算面积：

![长方形](test_images/rectangle.png?size=medium)

**计算过程：**
面积 = 长 × 宽 = 8 × 5 = 40

**答案：** 40平方厘米

这个答案是通过公式计算得出的。
/

## 第五题：复杂文本测试

/
这是一个复杂的应用题，需要分步计算。

**第一步：** 计算基础数值
**第二步：** 应用公式
**第三步：** 得出最终结果

**答案：** 最终结果是42

答案解析：通过三个步骤的计算，我们得到了正确的答案。
/

## 验证总结

/
通过以上测试，我们验证了"答案"文字替换图片功能：

1. **基础替换：** 单个"答案"文字正确替换 ✓
2. **多次替换：** 同一段落中多个"答案"都被替换 ✓  
3. **格式兼容：** 与粗体、特殊标记等格式兼容 ✓
4. **图文混合：** 与图片显示功能兼容 ✓
5. **复杂文本：** 在复杂文本环境中正常工作 ✓

**最终答案：** 功能测试全部通过！

所有的答案标签都应该显示为橙色的图片，而不是普通文字。
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终'答案'文字替换功能测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_answer_replacement_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎯 验证要点：")
        print("   - 检查所有'答案'文字是否被替换为橙色图片")
        print("   - 验证图片与文字的对齐效果")
        print("   - 确认与其他格式的兼容性")
        print("   - 测试图文混合显示效果")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_answer_replacement_test())
