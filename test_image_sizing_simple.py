#!/usr/bin/env python3
"""
测试图片尺寸调整功能（简化版）
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_image_sizing_simple():
    """测试图片尺寸调整功能（简化版）"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 图片尺寸调整功能测试

## 1. 预设尺寸选项

### 1.1 小尺寸 (size=small)

/
这是小尺寸的图片显示效果：

![小尺寸图片](test_images/math_diagram.png?size=small)

小尺寸适合用作图标或简单示意图。
/

### 1.2 中等尺寸 (size=medium)

/
这是中等尺寸的图片显示效果：

![中等尺寸图片](test_images/rectangle.png?size=medium)

中等尺寸是默认推荐的尺寸。
/

### 1.3 大尺寸 (size=large)

/
这是大尺寸的图片显示效果：

![大尺寸图片](test_images/calculation_steps.png?size=large)

大尺寸适合需要详细展示的重要图表。
/

### 1.4 原始尺寸 (size=original)

/
这是原始尺寸的图片显示效果：

![原始尺寸图片](test_images/math_diagram.png?size=original)

原始尺寸保持图片的原始比例。
/

## 2. 精确尺寸控制

### 2.1 指定宽度 (width=150)

/
指定图片宽度为150像素：

![指定宽度](test_images/rectangle.png?width=150)

通过指定宽度，高度会按比例自动调整。
/

### 2.2 指定高度 (height=100)

/
指定图片高度为100像素：

![指定高度](test_images/calculation_steps.png?height=100)

通过指定高度，宽度会按比例自动调整。
/

### 2.3 指定宽度和高度 (width=200&height=120)

/
同时指定图片宽度200像素和高度120像素：

![指定宽高](test_images/math_diagram.png?width=200&height=120)

注意：同时指定宽度和高度可能会导致图片变形。
/

## 3. 默认自动尺寸

### 3.1 自动适应 (无参数)

/
这是默认的自动尺寸调整效果：

![自动尺寸](test_images/rectangle.png)

系统会自动计算合适的尺寸。
/

## 4. 使用说明

### 4.1 支持的语法格式

/
**图片尺寸调整语法：**

1. `![描述](路径)` - 默认自动尺寸
2. `![描述](路径?size=small)` - 预设小尺寸
3. `![描述](路径?size=medium)` - 预设中等尺寸  
4. `![描述](路径?size=large)` - 预设大尺寸
5. `![描述](路径?size=original)` - 原始尺寸
6. `![描述](路径?width=200)` - 指定宽度
7. `![描述](路径?height=150)` - 指定高度
8. `![描述](路径?width=200&height=150)` - 指定宽高

**使用建议：**
- 小尺寸：适合图标、简单示意图
- 中等尺寸：适合一般教学图片（推荐）
- 大尺寸：适合重要图表、复杂图形
- 原始尺寸：适合高质量图片展示
- 精确控制：适合特殊布局需求
/

## 总结

/
图片尺寸调整功能为教学材料制作提供了灵活的控制选项。

![总结图](test_images/math_diagram.png?size=medium)

所有图片都会自动居中显示在答案框中，并且不会超出框的边界。
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成图片尺寸调整测试PDF（简化版）...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_image_sizing_simple.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🖼️ 测试内容包括：")
        print("   - 预设尺寸选项 (small, medium, large, original)")
        print("   - 精确尺寸控制 (width, height)")
        print("   - 默认自动尺寸")
        print("   - 使用说明和语法示例")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_image_sizing_simple())
