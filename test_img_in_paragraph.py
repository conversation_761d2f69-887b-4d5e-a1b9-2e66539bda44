#!/usr/bin/env python3
"""
测试ReportLab Paragraph中的img标签是否真的工作
"""

from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_JUSTIFY
import os

def test_img_in_paragraph():
    """测试img标签在Paragraph中的实际效果"""
    
    # 创建PDF文档
    doc = SimpleDocTemplate("test_img_in_paragraph.pdf", pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # 检查图片是否存在
    img_path = os.path.abspath("answer_images/answer_label.png")
    print(f"图片路径: {img_path}")
    print(f"图片存在: {os.path.exists(img_path)}")
    
    if os.path.exists(img_path):
        # 测试1：简单的img标签
        story.append(Paragraph("测试1：简单img标签", styles['Heading2']))
        text1 = f'这是文字 <img src="{img_path}" width="60" height="20"/> 后面的文字'
        para1 = Paragraph(text1, styles['Normal'])
        story.append(para1)
        story.append(Spacer(1, 12))
        
        # 测试2：模拟答案替换
        story.append(Paragraph("测试2：模拟答案替换", styles['Heading2']))
        text2 = f'这是一个计算题。<br/><b><img src="{img_path}" width="60" height="20"/>：</b> 42<br/>这道题很简单。'
        para2 = Paragraph(text2, styles['Normal'])
        story.append(para2)
        story.append(Spacer(1, 12))
        
        # 测试3：在楷体样式中使用
        story.append(Paragraph("测试3：楷体样式中的img", styles['Heading2']))
        kaiti_style = ParagraphStyle(
            'KaiTi',
            parent=styles['Normal'],
            fontName='Helvetica',  # 使用可用字体
            fontSize=12,
            leading=15,
            alignment=TA_JUSTIFY
        )
        text3 = f'这是楷体样式的文字，包含<img src="{img_path}" width="60" height="20"/>标签。'
        para3 = Paragraph(text3, kaiti_style)
        story.append(para3)
        story.append(Spacer(1, 12))
        
        # 测试4：不同的valign设置
        story.append(Paragraph("测试4：不同的valign设置", styles['Heading2']))
        text4 = f'valign=top: <img src="{img_path}" width="60" height="20" valign="top"/><br/>'
        text4 += f'valign=middle: <img src="{img_path}" width="60" height="20" valign="middle"/><br/>'
        text4 += f'valign=bottom: <img src="{img_path}" width="60" height="20" valign="bottom"/><br/>'
        text4 += f'无valign: <img src="{img_path}" width="60" height="20"/>'
        para4 = Paragraph(text4, styles['Normal'])
        story.append(para4)
        story.append(Spacer(1, 12))
        
        # 测试5：检查HTML转义
        story.append(Paragraph("测试5：HTML转义检查", styles['Heading2']))
        text5 = f'原始文本：答案<br/>替换后：<img src="{img_path}" width="60" height="20" valign="middle"/>'
        para5 = Paragraph(text5, styles['Normal'])
        story.append(para5)
        
    else:
        story.append(Paragraph("错误：找不到答案图片文件", styles['Normal']))
    
    try:
        # 构建PDF
        doc.build(story)
        print("✅ 测试PDF生成成功: test_img_in_paragraph.pdf")
        
        # 自动打开PDF
        import subprocess
        import platform
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["open", "test_img_in_paragraph.pdf"])
            print("🔍 已自动打开测试PDF文件")
            
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_img_in_paragraph()
