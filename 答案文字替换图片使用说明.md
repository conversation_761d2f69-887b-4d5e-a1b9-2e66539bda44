# "答案"文字替换图片功能使用说明

## 概述

我已经为你的PDF生成系统添加了一个特殊功能：当文本中出现"答案"二字时，会自动替换为橙色的图片标签。这个功能让教学材料中的答案部分更加醒目和专业。

## 更新说明

**最新版本功能：**
- ✅ 真正的图片替换：现在"答案"文字会被实际的图片标签替换
- ✅ 智能文本分割：自动将包含"答案"的文本分割为文字和图片混合内容
- ✅ 完美布局：图片和文字自然排列，保持良好的视觉效果
- ✅ 自动尺寸：答案标签图片自动调整为合适大小

## 功能特性

### 🎯 自动识别替换
- **智能识别**：系统自动识别文本中的"答案"二字
- **无需标记**：不需要任何特殊语法或标记
- **全文适用**：在答案及解析框中的任何位置都会生效
- **保持格式**：替换后的图片与周围文字自然融合

### 🎨 视觉效果
- **橙色背景**：使用橙色 (#FF8C00) 圆角矩形背景
- **白色文字**：白色"答案"文字，清晰易读
- **圆角设计**：现代化的圆角矩形样式
- **适中尺寸**：80×26像素，与文字高度匹配

### 📝 兼容性
- **格式兼容**：与粗体、斜体等格式完美兼容
- **位置灵活**：可以出现在段落的任何位置
- **多次使用**：同一段落中可以有多个"答案"标签

## 使用示例

### 基本用法
```markdown
/
这是一个简单的计算题。

**答案：** 42

这道题的答案很容易计算出来。
/
```

### 与其他格式混合
```markdown
/
**计算过程：**
1. 第一步：5 + 3 = 8
2. 第二步：8 × 2 = 16

**答案：** 16

答案解析：按照运算顺序进行计算。
/
```

### 多个答案标签
```markdown
/
**第一题答案：** A
**第二题答案：** B  
**第三题答案：** C

所有答案都已标注完毕。
/
```

### 在不同上下文中使用
```markdown
/
这道题有多种解法，但答案是唯一的。

**方法一：** 直接计算得出答案
**方法二：** 通过公式推导得出答案

无论用哪种方法，最终答案都是一样的。
/
```

## 技术实现

### 处理流程
1. **文本扫描**：在处理答案框内容时扫描"答案"文字
2. **图片替换**：将"答案"文字替换为HTML img标签
3. **图片加载**：系统自动加载预设的答案标签图片
4. **渲染显示**：ReportLab将图片与文字一起渲染

### 图片资源
系统会自动查找以下答案标签图片：
- `answer_images/answer_label.png` - 标准尺寸 (120×40)
- `answer_images/answer_label_small.png` - 小尺寸 (80×26)
- `answer_images/answer_label_large.png` - 大尺寸 (150×50)

### 自动选择
- 系统优先使用标准尺寸的答案标签
- 如果标准尺寸不存在，会尝试其他尺寸
- 在答案框中使用小尺寸版本以适应文本行高

## 自定义答案图片

### 创建自定义图片
如果你想使用自定义的答案标签图片，可以：

1. **替换现有图片**：将你的图片保存为 `answer_images/answer_label.png`
2. **保持尺寸比例**：建议使用 3:1 的宽高比
3. **使用PNG格式**：支持透明背景，显示效果更好

### 推荐规格
- **标准尺寸**：120×40像素
- **小尺寸**：80×26像素（用于答案框）
- **大尺寸**：150×50像素
- **格式**：PNG（支持透明背景）
- **背景色**：橙色 (#FF8C00) 或其他醒目颜色

## 使用场景

### 📚 教学材料
```markdown
/
根据题目要求计算圆的面积。

**已知：** 半径 r = 5cm
**公式：** S = πr²
**计算：** S = 3.14 × 5² = 78.5

**答案：** 78.5平方厘米
/
```

### 📝 练习题
```markdown
/
选择题：下列哪个是正确的？
A. 2 + 2 = 5
B. 2 + 2 = 4  
C. 2 + 2 = 3

**答案：** B

答案解析：基本的加法运算。
/
```

### 🧮 计算题
```markdown
/
计算下列表达式的值：

(8 + 2) × 3 - 5 = ?

**步骤：**
1. 括号内：8 + 2 = 10
2. 乘法：10 × 3 = 30  
3. 减法：30 - 5 = 25

**答案：** 25
/
```

### 📖 问答题
```markdown
/
**问题：** 什么是光合作用？

**答案：** 光合作用是植物利用阳光、二氧化碳和水制造葡萄糖和氧气的过程。

这个答案涵盖了光合作用的基本要素。
/
```

## 注意事项

### ✅ 推荐做法
1. **统一使用**：在同一文档中保持答案标签的一致性
2. **适当间距**：在答案标签前后留有适当的空格
3. **清晰标注**：使用"**答案：**"的格式更加清晰
4. **逻辑顺序**：将答案放在解题过程之后

### ⚠️ 注意事项
1. **文字匹配**：只有完整的"答案"二字才会被替换
2. **图片依赖**：需要确保答案图片文件存在
3. **尺寸适配**：图片尺寸会自动适配文本行高
4. **格式保持**：替换不会影响其他文本格式

## 与其他功能的兼容性

### 🎯 完美兼容
- ✅ **粗体文本**：`**答案：**` 
- ✅ **斜体文本**：`*答案*`
- ✅ **双括号特殊格式**：`（（答案说明））`
- ✅ **图片显示**：与图片混合使用
- ✅ **多段落内容**：在任何段落中都可使用
- ✅ **尺寸调整**：与图片尺寸调整功能兼容

### 🔧 技术优势
- **处理顺序优化**：先处理Markdown格式，再处理答案替换
- **错误处理**：图片加载失败时保持原文字显示
- **性能优化**：只在需要时加载答案图片
- **内存管理**：图片资源合理使用和释放

## 总结

"答案"文字替换图片功能为教学材料制作提供了专业的视觉效果：

- **提升视觉效果**：橙色标签让答案更加醒目
- **保持专业性**：统一的视觉风格提升材料质量
- **使用简便**：无需特殊语法，直接输入"答案"即可
- **高度兼容**：与所有现有功能完美配合

这个功能让你的教学材料更加专业和美观，帮助学生更容易识别和关注答案部分！
