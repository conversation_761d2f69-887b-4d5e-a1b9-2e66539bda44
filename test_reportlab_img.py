#!/usr/bin/env python3
"""
测试ReportLab是否支持img标签
"""

from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
import os

def test_reportlab_img():
    """测试ReportLab的img标签支持"""
    
    # 创建PDF文档
    doc = SimpleDocTemplate("test_img_tag.pdf", pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # 测试不同的img标签格式
    img_path = os.path.abspath("answer_images/answer_label.png")
    
    if os.path.exists(img_path):
        print(f"图片路径: {img_path}")
        
        # 测试1：标准HTML img标签
        try:
            text1 = f'这是文字 <img src="{img_path}" width="60" height="20"/> 后面还有文字'
            para1 = Paragraph(text1, styles['Normal'])
            story.append(para1)
            print("✅ 标准img标签测试添加成功")
        except Exception as e:
            print(f"❌ 标准img标签测试失败: {e}")
        
        # 测试2：简化img标签
        try:
            text2 = f'这是文字 <img src="{img_path}"/> 后面还有文字'
            para2 = Paragraph(text2, styles['Normal'])
            story.append(para2)
            print("✅ 简化img标签测试添加成功")
        except Exception as e:
            print(f"❌ 简化img标签测试失败: {e}")
        
        # 测试3：使用file://协议
        try:
            text3 = f'这是文字 <img src="file://{img_path}" width="60" height="20"/> 后面还有文字'
            para3 = Paragraph(text3, styles['Normal'])
            story.append(para3)
            print("✅ file://协议测试添加成功")
        except Exception as e:
            print(f"❌ file://协议测试失败: {e}")
    
    # 添加普通文字作为对比
    story.append(Paragraph("这是普通文字，用来对比效果", styles['Normal']))
    
    try:
        # 构建PDF
        doc.build(story)
        print("✅ PDF生成成功: test_img_tag.pdf")
        
        # 自动打开PDF
        import subprocess
        import platform
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["open", "test_img_tag.pdf"])
            print("🔍 已自动打开PDF文件")
            
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_reportlab_img()
