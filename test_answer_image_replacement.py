#!/usr/bin/env python3
"""
测试"答案"文字替换为图片的功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_image_replacement():
    """测试"答案"文字替换为图片的功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# "答案"文字替换图片功能测试

## 第一题：基础计算

计算：5 + 3 = ?

/
这是一个简单的加法题目。

答案是8。

这道题考查基本的加法运算能力。
/

## 第二题：乘法运算

计算：6 × 7 = ?

/
这是一个乘法题目，需要用到九九乘法表。

6 × 7 = 42

答案：42

学生需要熟练掌握乘法表。
/

## 第三题：应用题

小明有15个苹果，吃掉了5个，还剩多少个？

/
这是一个减法应用题。

**计算过程：**
15 - 5 = 10

**答案：** 10个苹果

这道题考查学生的减法运算和实际应用能力。
/

## 第四题：多个答案的情况

下列哪些数字是偶数？2, 3, 4, 5, 6

/
偶数是能被2整除的数字。

**分析：**
- 2 ÷ 2 = 1，所以2是偶数
- 3 ÷ 2 = 1.5，所以3不是偶数  
- 4 ÷ 2 = 2，所以4是偶数
- 5 ÷ 2 = 2.5，所以5不是偶数
- 6 ÷ 2 = 3，所以6是偶数

**答案：** 2、4、6是偶数

**总结答案：** 偶数有2、4、6三个
/

## 第五题：复杂计算

计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要按照运算顺序来计算。

**第一步：** 计算括号内容
(8 + 4) = 12

**第二步：** 计算乘除法（从左到右）
12 × 3 = 36
6 ÷ 2 = 3

**第三步：** 计算减法
36 - 3 = 33

**最终答案：** 33

运算顺序很重要：先括号，再乘除，最后加减。答案是33。
/

## 第六题：几何问题

一个正方形的边长是5cm，求它的面积。

/
正方形面积的计算公式是：面积 = 边长 × 边长

**已知：** 边长 = 5cm

**计算：** 面积 = 5 × 5 = 25平方厘米

**答案：** 25平方厘米

这是几何图形面积计算的基础题目，答案很明确。
/

## 第七题：对比测试（无"答案"字样）

计算：10 ÷ 2 = ?

/
这是一个简单的除法题目。

10 ÷ 2 = 5

结果是5。这道题比较简单，学生应该能够快速计算出结果。
/

## 总结

/
通过以上测试，我们验证了"答案"文字替换图片的功能：

1. **单个"答案"替换**：文本中的"答案"会被替换为橙色标签图片
2. **多个"答案"替换**：一个段落中的多个"答案"都会被替换
3. **上下文保持**：答案前后的文字正常显示
4. **格式兼容**：与其他格式化文本（粗体、特殊标记等）兼容
5. **对比效果**：没有"答案"字样的段落正常显示

这个功能让教学材料中的答案更加醒目和专业。最终答案就是这样显示的！
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成答案文字替换图片测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_image_replacement.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🖼️ 测试内容包括：")
        print("   - 单个'答案'文字替换为图片")
        print("   - 多个'答案'文字替换")
        print("   - 答案前后文字保持")
        print("   - 格式兼容性测试")
        print("   - 对比测试（无'答案'字样）")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_image_replacement())
