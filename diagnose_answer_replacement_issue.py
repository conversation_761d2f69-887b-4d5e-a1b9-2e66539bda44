#!/usr/bin/env python3
"""
诊断"答案"文字替换功能的问题
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def diagnose_answer_replacement():
    """诊断答案替换功能"""
    
    print("🔍 开始诊断答案文字替换功能...")
    
    # 1. 检查答案图片文件是否存在
    print("\n1. 检查答案图片文件...")
    answer_image_paths = [
        "answer_images/answer_label.png",
        "answer_images/answer_label_small.png", 
        "answer_images/answer_label_large.png"
    ]
    
    for path in answer_image_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            print(f"   ✅ {path} 存在 ({file_size} 字节)")
        else:
            print(f"   ❌ {path} 不存在")
    
    # 2. 测试PDF服务实例化
    print("\n2. 测试PDF服务...")
    try:
        pdf_service = PDFService()
        print("   ✅ PDF服务实例化成功")
    except Exception as e:
        print(f"   ❌ PDF服务实例化失败: {e}")
        return
    
    # 3. 测试简单的答案替换
    print("\n3. 测试简单答案替换...")
    simple_test_content = """
# 简单测试

## 测试题

计算：1 + 1 = ?

/
这是最简单的加法题目。

答案是2。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        pdf_path = await pdf_service.generate_pdf(
            content=simple_test_content,
            config=config,
            filename="diagnose_simple_answer_test.pdf"
        )
        print(f"   ✅ 简单测试PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"   📄 文件大小: {file_size} 字节")
        
    except Exception as e:
        print(f"   ❌ 简单测试PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 测试你的实际内容格式
    print("\n4. 请提供你的实际内容进行测试...")
    print("   请将你在实际操作中使用的Markdown内容复制到下面的测试中")
    
    # 模拟一些可能的实际使用场景
    real_world_tests = [
        {
            "name": "标准格式",
            "content": """
# 数学练习

## 第一题

计算：5 + 3 = ?

/
这道题很简单。

答案是8。
/
"""
        },
        {
            "name": "复杂格式",
            "content": """
# 练习题

## 应用题

小明有10个苹果，吃了3个，还剩几个？

/
这是一个减法应用题。

计算过程：10 - 3 = 7

**答案：** 7个苹果

所以答案是7个。
/
"""
        },
        {
            "name": "多段落格式",
            "content": """
# 测试

## 题目

计算面积

/
长方形的长是5cm，宽是3cm。

面积 = 长 × 宽 = 5 × 3 = 15平方厘米

答案：15平方厘米

这就是最终答案。
/
"""
        }
    ]
    
    for i, test in enumerate(real_world_tests, 1):
        print(f"\n5.{i} 测试{test['name']}...")
        try:
            pdf_path = await pdf_service.generate_pdf(
                content=test['content'],
                config=config,
                filename=f"diagnose_{test['name']}_test.pdf"
            )
            print(f"   ✅ {test['name']}测试PDF生成成功: {pdf_path}")
            
        except Exception as e:
            print(f"   ❌ {test['name']}测试PDF生成失败: {e}")
    
    # 6. 检查代码逻辑
    print("\n6. 检查代码逻辑...")
    
    # 测试AnswerAnalysisBox类的文本处理
    try:
        from backend.app.services.pdf_service import AnswerAnalysisBox
        from reportlab.lib.styles import ParagraphStyle
        
        # 创建测试样式
        test_style = ParagraphStyle('Test')
        
        # 创建AnswerAnalysisBox实例
        test_box = AnswerAnalysisBox("这是测试文本，包含答案字样。", test_style)
        
        # 测试文本处理方法
        if hasattr(test_box, '_process_text_with_answer_replacement'):
            result = test_box._process_text_with_answer_replacement("这是测试答案。")
            print(f"   ✅ 文本处理方法存在，结果: {result}")
        else:
            print("   ❌ 文本处理方法不存在")
            
        # 测试图片创建方法
        if hasattr(test_box, '_create_answer_image'):
            img = test_box._create_answer_image(400)
            if img:
                print("   ✅ 答案图片创建成功")
            else:
                print("   ❌ 答案图片创建失败")
        else:
            print("   ❌ 图片创建方法不存在")
            
    except Exception as e:
        print(f"   ❌ 代码逻辑检查失败: {e}")
    
    # 7. 提供调试建议
    print("\n7. 调试建议:")
    print("   📝 请检查以下几点：")
    print("   1. 确保你使用的是 /内容/ 格式的答案框")
    print("   2. 确保'答案'是完整的中文字符，不是其他相似字符")
    print("   3. 确保答案图片文件存在于正确位置")
    print("   4. 检查是否有其他错误信息")
    print("   5. 尝试使用最简单的测试内容")
    
    print("\n🔍 诊断完成！请查看生成的测试PDF文件来验证功能。")

if __name__ == "__main__":
    asyncio.run(diagnose_answer_replacement())
