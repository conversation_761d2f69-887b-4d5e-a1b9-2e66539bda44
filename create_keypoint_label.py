#!/usr/bin/env python3
"""
创建重难点标签图片
生成蓝色系的重难点标签，与答案标签类似但使用不同颜色
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_keypoint_label():
    """创建重难点标签图片"""
    
    # 图片尺寸（与答案标签相同）
    width = 80
    height = 30
    
    # 创建图片
    img = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # 蓝色系颜色配置
    bg_color = (0, 114, 189)  # #0072bd 深蓝色背景
    text_color = (255, 255, 255)  # 白色文字
    border_color = (0, 114, 189)  # 深蓝色边框
    
    # 绘制圆角矩形背景
    corner_radius = 8
    
    # 绘制圆角矩形（通过绘制多个形状组合）
    # 主矩形
    draw.rectangle([corner_radius, 0, width-corner_radius, height], fill=bg_color)
    draw.rectangle([0, corner_radius, width, height-corner_radius], fill=bg_color)
    
    # 四个圆角
    draw.ellipse([0, 0, corner_radius*2, corner_radius*2], fill=bg_color)
    draw.ellipse([width-corner_radius*2, 0, width, corner_radius*2], fill=bg_color)
    draw.ellipse([0, height-corner_radius*2, corner_radius*2, height], fill=bg_color)
    draw.ellipse([width-corner_radius*2, height-corner_radius*2, width, height], fill=bg_color)
    
    # 尝试加载字体
    try:
        # 尝试使用系统中文字体
        font_paths = [
            "/System/Library/Fonts/STHeiti Light.ttc",  # macOS
            "/System/Library/Fonts/Helvetica.ttc",
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # Windows 宋体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 14)
                    break
                except:
                    continue
        
        if font is None:
            font = ImageFont.load_default()
            
    except Exception as e:
        print(f"字体加载失败，使用默认字体: {e}")
        font = ImageFont.load_default()
    
    # 绘制文字
    text = "重难点"
    
    # 计算文字位置（居中）
    try:
        # 新版本PIL
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    except AttributeError:
        # 旧版本PIL
        text_width, text_height = draw.textsize(text, font=font)
    
    text_x = (width - text_width) // 2
    text_y = (height - text_height) // 2
    
    # 绘制文字
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    return img

def main():
    """主函数"""
    print("🎨 创建重难点标签图片...")
    
    # 确保目录存在
    os.makedirs("keypoint_images", exist_ok=True)
    
    # 创建重难点标签图片
    img = create_keypoint_label()
    
    # 保存不同尺寸的图片
    sizes = [
        ("keypoint_images/keypoint_label.png", 1.0),
        ("keypoint_images/keypoint_label_small.png", 0.7),
        ("keypoint_images/keypoint_label_large.png", 1.3),
    ]
    
    for filename, scale in sizes:
        if scale != 1.0:
            new_width = int(img.width * scale)
            new_height = int(img.height * scale)
            scaled_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            scaled_img.save(filename)
        else:
            img.save(filename)
        
        print(f"✅ 已保存: {filename}")
    
    print("🎉 重难点标签图片创建完成！")

if __name__ == "__main__":
    main()
