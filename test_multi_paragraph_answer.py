#!/usr/bin/env python3
"""
测试多段落答案及解析样式的脚本
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_multi_paragraph_answer():
    """测试多段落答案及解析样式"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 多段落答案及解析测试

## 单行格式测试

计算：5 × 3 + 2 = ?

/这是一个简单的运算顺序题目。答案是17。/

## 多行格式测试

### 第一题：复杂计算

计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要按照运算顺序来计算：

首先计算括号内的内容：8 + 4 = 12

然后进行乘除运算：12 × 3 = 36，6 ÷ 2 = 3

最后进行减法：36 - 3 = 33

所以答案是33。这道题考查的是混合运算的顺序。
/

### 第二题：应用题

小明有24个苹果，要平均分给6个朋友，每个朋友能分到几个苹果？

/
这是一个除法应用题。

已知条件：
- 总苹果数：24个
- 朋友人数：6个
- 要求：平均分配

计算过程：24 ÷ 6 = 4

答案：每个朋友能分到4个苹果。

这道题考查的是除法的实际应用，帮助学生理解平均分配的概念。
/

### 第三题：几何问题

一个长方形的长是8cm，宽是5cm，求它的面积和周长。

/
这道题需要分别计算面积和周长：

**面积计算：**
长方形面积公式：面积 = 长 × 宽
代入数值：面积 = 8 × 5 = 40平方厘米

**周长计算：**
长方形周长公式：周长 = 2 × (长 + 宽)
代入数值：周长 = 2 × (8 + 5) = 2 × 13 = 26厘米

**答案总结：**
- 面积：40平方厘米
- 周长：26厘米

这道题考查学生对长方形基本公式的掌握和应用能力。（（几何图形的计算是数学基础知识的重要组成部分））
/

## 格式混合测试

### 短答案

3 + 5 = ?

/答案是8。/

### 长答案

如果一个班级有30名学生，其中男生占40%，女生占60%，请分别计算男生和女生的人数。

/
这是一个百分比应用题，需要分别计算男生和女生的人数。

**男生人数计算：**
男生人数 = 总人数 × 男生比例
男生人数 = 30 × 40% = 30 × 0.4 = 12人

**女生人数计算：**
女生人数 = 总人数 × 女生比例  
女生人数 = 30 × 60% = 30 × 0.6 = 18人

**验证：**
12 + 18 = 30 ✓（总数正确）

**答案：**
- 男生：12人
- 女生：18人

这道题考查学生对百分比概念的理解和计算能力，同时培养验证答案的好习惯。
/

## 总结

通过以上测试，我们验证了答案及解析框支持：
- 单行内容
- 多行内容  
- 多段落内容
- 格式化文本（粗体、特殊标记等）
- 自动换行和高度调整
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成多段落答案及解析测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_multi_paragraph_answer.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("📋 测试内容包括：")
        print("   - 单行答案格式：/内容/")
        print("   - 多行答案格式：/\\n内容\\n内容\\n/")
        print("   - 多段落内容支持")
        print("   - 格式化文本支持")
        print("   - 自动换行和高度调整")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_multi_paragraph_answer())
