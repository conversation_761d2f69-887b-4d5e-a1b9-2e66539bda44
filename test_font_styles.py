#!/usr/bin/env python3
"""
测试ReportLab的字体样式支持
"""

from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

def test_font_styles():
    """测试不同的字体样式"""
    
    # 创建PDF文档
    doc = SimpleDocTemplate("test_font_styles.pdf", pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # 测试不同的样式
    tests = [
        ('基础文本', '这是普通的答案文字'),
        ('颜色文本', '<font color="orange">答案</font>'),
        ('背景色文本', '<font backColor="orange">答案</font>'),
        ('颜色+背景', '<font color="white" backColor="orange">答案</font>'),
        ('粗体+颜色', '<font color="orange"><b>答案</b></font>'),
        ('粗体+背景', '<font backColor="orange"><b>答案</b></font>'),
        ('完整样式', '<font color="white" backColor="#FF8C00"><b> 答案 </b></font>'),
        ('Unicode标记', '🔶答案🔶'),
        ('方括号标记', '【答案】'),
        ('圆括号标记', '（答案）'),
    ]
    
    for title, text in tests:
        story.append(Paragraph(f"<b>{title}：</b>", styles['Normal']))
        story.append(Paragraph(f"测试文字：{text}", styles['Normal']))
        story.append(Spacer(1, 12))
    
    try:
        # 构建PDF
        doc.build(story)
        print("✅ 字体样式测试PDF生成成功: test_font_styles.pdf")
        
        # 自动打开PDF
        import subprocess
        import platform
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["open", "test_font_styles.pdf"])
            print("🔍 已自动打开测试PDF文件")
            
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_font_styles()
