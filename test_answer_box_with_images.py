#!/usr/bin/env python3
"""
测试答案及解析框中的图片显示功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_box_with_images():
    """测试答案框中的图片显示功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 数学练习题（含图片解析）

## 第一题：函数图像

观察下面的函数图像，说明这是什么函数？

![数学图表](test_images/math_diagram.png)

/
这是一个二次函数的图像，具体是 y = x² 函数。

![数学图表](test_images/math_diagram.png)

从图像可以看出：
1. 这是一个开口向上的抛物线
2. 顶点在原点 (0,0)
3. 函数关于y轴对称

二次函数的一般形式是 y = ax² + bx + c，这里 a=1, b=0, c=0。
/

## 第二题：几何计算

计算下图长方形的面积和周长：

/
根据题目给出的长方形尺寸：

![长方形](test_images/rectangle.png)

**面积计算：**
面积 = 长 × 宽 = 8cm × 5cm = 40平方厘米

**周长计算：**
周长 = 2 × (长 + 宽) = 2 × (8 + 5) = 26厘米

**答案：**
- 面积：40平方厘米
- 周长：26厘米
/

## 第三题：复合运算

计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要按照运算顺序来计算，让我们分步骤进行：

![计算步骤](test_images/calculation_steps.png)

**详细计算过程：**

**第一步：** 计算括号内的加法
8 + 4 = 12

**第二步：** 计算乘法
12 × 3 = 36

**第三步：** 计算除法
6 ÷ 2 = 3

**第四步：** 计算减法
36 - 3 = 33

**最终答案：** 33

这道题考查的是混合运算的顺序：先算括号，再算乘除，最后算加减。
/

## 第四题：图文混合解析

如果一个正方形的面积是25平方厘米，求它的边长和周长。

/
这是一个逆向思维的几何题目。

**已知条件：** 正方形面积 = 25平方厘米

**求边长：**
因为正方形面积 = 边长²
所以 边长² = 25
因此 边长 = √25 = 5厘米

**求周长：**
正方形周长 = 4 × 边长 = 4 × 5 = 20厘米

![长方形](test_images/rectangle.png)

上图虽然是长方形，但我们可以想象如果长和宽都是5厘米，就变成了正方形。

**答案总结：**
- 边长：5厘米
- 周长：20厘米

这道题考查学生对平方根的理解和正方形基本公式的应用。（（几何图形的计算要理解公式的含义））
/

## 第五题：纯文字解析（对比测试）

计算：15 ÷ 3 + 2 × 4 = ?

/
这是一个没有图片的普通解析，用来对比测试。

**计算过程：**
1. 先算除法：15 ÷ 3 = 5
2. 再算乘法：2 × 4 = 8  
3. 最后算加法：5 + 8 = 13

**答案：** 13

这道题比较简单，主要考查基本的四则运算顺序。
/

## 总结

通过以上测试，我们验证了答案及解析框支持：
- ✅ 纯文字内容
- ✅ 图片显示
- ✅ 图文混合内容
- ✅ 多段落格式
- ✅ 格式化文本
- ✅ 自动布局和高度调整
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成带图片的答案及解析测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_box_with_images.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🖼️ 测试内容包括：")
        print("   - 答案框中的图片显示")
        print("   - 图文混合内容")
        print("   - 图片居中对齐")
        print("   - 图片自动缩放")
        print("   - 多段落图文布局")
        print("   - 纯文字内容对比")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_box_with_images())
