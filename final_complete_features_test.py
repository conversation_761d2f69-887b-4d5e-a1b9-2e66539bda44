#!/usr/bin/env python3
"""
最终完整功能测试 - 展示所有样式功能的完整使用
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_complete_features_test():
    """最终完整功能测试"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建综合测试内容
    test_content = """
# 小学数学综合练习册（完整版）

## 第一章：基础运算

### 1.1 加减法练习

1. 计算：25 + 37 = ?

/
这是一个两位数加法题目。

**计算过程：**
25 + 37 = 62

答案是62。这道题考查学生的基础加法运算能力。
/

2. 计算：83 - 29 = ?

/
这是一个需要借位的减法题目。

**计算步骤：**
- 个位：3 - 9 不够减，需要从十位借1
- 个位变成：13 - 9 = 4  
- 十位：8 - 1 - 2 = 5

**答案：** 54

借位减法是重要的计算技巧。（（借位运算需要多练习））
/

### 1.2 乘除法练习

3. 计算：6 × 8 = ?

/
这是九九乘法表中的基础题目。

6 × 8 = 48

答案：48
/

4. 计算：144 ÷ 12 = ?

/
这是一个整除的除法题目。

**方法一：** 直接计算
144 ÷ 12 = 12

**方法二：** 分解计算  
144 = 12 × 12，所以答案是12

**验证：** 12 × 12 = 144 ✓

最终答案是12。
/

## 第二章：应用题

### 2.1 购物问题

小明去商店买文具，买了3支铅笔（每支2元）和2块橡皮（每块1.5元），一共需要多少钱？

/
这是一个多步骤的应用题。

**第一步：** 计算铅笔总价
铅笔总价 = 3 × 2 = 6元

**第二步：** 计算橡皮总价
橡皮总价 = 2 × 1.5 = 3元

**第三步：** 计算总花费
总花费 = 6 + 3 = 9元

**答案：** 小明一共需要9元

这道题考查学生的分步计算能力和小数乘法运算。
/

### 2.2 时间问题

如果现在是上午9:15，再过2小时30分钟是几点？

/
这是一个时间加法问题。

**计算过程：**
起始时间：9:15
增加时间：2小时30分钟

**分步计算：**
1. 先加小时：9:15 + 2小时 = 11:15
2. 再加分钟：11:15 + 30分钟 = 11:45

**答案：** 11:45（上午11点45分）

时间计算要注意分钟满60进位到小时。（（时间单位换算很重要））
/

## 第三章：几何图形

### 3.1 面积计算

5. 一个长方形的长是12cm，宽是8cm，求它的面积。

/
长方形面积公式：面积 = 长 × 宽

代入数值：面积 = 12 × 8 = 96平方厘米

答案：96平方厘米
/

6. 一个正方形的周长是20cm，求它的面积。

/
这道题需要先求出边长，再计算面积。

**第一步：** 求边长
正方形周长 = 4 × 边长
20 = 4 × 边长
边长 = 20 ÷ 4 = 5cm

**第二步：** 求面积
正方形面积 = 边长 × 边长 = 5 × 5 = 25平方厘米

**答案：** 25平方厘米

这道题考查学生对正方形周长和面积公式的综合运用。
/

## 第四章：图片与图表

### 4.1 函数图像题

观察下面的函数图像，说明这是什么函数？

/
这是一个二次函数的图像。

![数学图表](test_images/math_diagram.png?size=medium)

从图像可以看出这是 y = x² 函数，具有以下特点：
- 开口向上的抛物线
- 顶点在原点 (0,0)
- 关于y轴对称

**答案：** 这是二次函数 y = x²
/

### 4.2 几何图形题

根据下图计算长方形的面积和周长：

/
根据图中给出的尺寸：

![长方形](test_images/rectangle.png?size=large)

**已知条件：**
- 长 = 8cm
- 宽 = 5cm

**面积计算：**
面积 = 长 × 宽 = 8 × 5 = 40平方厘米

**周长计算：**
周长 = 2 × (长 + 宽) = 2 × (8 + 5) = 26厘米

**最终答案：**
- 面积：40平方厘米
- 周长：26厘米
/

## 第五章：综合练习

### 5.1 混合运算

计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要严格按照运算顺序进行：

![计算步骤](test_images/calculation_steps.png?size=small)

**详细步骤：**

**第一步：** 计算括号内容
(8 + 4) = 12

**第二步：** 从左到右计算乘除法
- 12 × 3 = 36
- 6 ÷ 2 = 3

**第三步：** 计算减法
36 - 3 = 33

**最终答案：** 33

运算顺序口诀：先括号，后乘除，最后加减。答案是33。
/

### 5.2 逆向思维题

如果一个正方形的面积是36平方厘米，求它的边长和周长。

/
这是一个从面积求边长的逆向思维题。

**已知：** 正方形面积 = 36平方厘米

**求边长：**
∵ 正方形面积 = 边长²
∴ 边长² = 36
∴ 边长 = √36 = 6厘米

**求周长：**
正方形周长 = 4 × 边长 = 4 × 6 = 24厘米

**验证：** 6² = 36 ✓

**最终答案：**
- 边长：6厘米
- 周长：24厘米

这道题考查学生的逆向思维能力，答案计算正确。
/

## 总结

/
通过这套综合练习，我们复习了小学数学的主要内容：

**基础运算：**
- 加减法（包括借位运算）
- 乘除法（包括九九乘法表）

**应用题：**
- 购物计算问题
- 时间计算问题

**几何图形：**
- 长方形和正方形的面积、周长计算
- 图形识别和分析

**综合能力：**
- 混合运算的顺序掌握
- 逆向思维问题解决

**技术展示：**
- 图文混合的答案解析
- 多种尺寸的图片显示
- 专业的答案标签效果

所有的答案都经过仔细计算和验证，确保准确性。这套练习册展示了完整的功能特性，包括文字、图片、格式化文本和答案标签的完美结合。（（综合练习有助于全面提升数学能力））

最终答案：这是一个功能完整、样式丰富的数学练习册！
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终完整功能测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_complete_features_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎉 最终完整功能测试完成！")
        print("📋 包含的完整功能：")
        print("   ✅ 一级标题（带背景图片）")
        print("   ✅ 二级和三级标题")
        print("   ✅ 编号列表（带圆形背景）")
        print("   ✅ 答案及解析框（文本内容）")
        print("   ✅ 答案及解析框（图片显示）")
        print("   ✅ 答案及解析框（图文混合）")
        print("   ✅ 图片尺寸调整（small, medium, large）")
        print("   ✅ '答案'文字替换为图片标签")
        print("   ✅ 双括号文本（橙色楷体）")
        print("   ✅ 粗体和格式化文本")
        print("   ✅ 图片自动缩放和居中")
        print("   ✅ 自动换行和高度调整")
        print("   ✅ 所有样式的完美融合")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_complete_features_test())
