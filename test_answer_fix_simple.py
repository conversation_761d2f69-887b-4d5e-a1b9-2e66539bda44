#!/usr/bin/env python3
"""
简单测试答案图片修复
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_fix():
    """测试答案图片修复"""
    
    print("🔍 测试答案图片修复...")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查答案图片文件
    answer_paths = [
        "answer_images/answer_label.png",
        "answer_images/answer_label_small.png"
    ]
    
    print("\n检查答案图片文件:")
    for path in answer_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"   ✅ {path} ({size} 字节)")
        else:
            print(f"   ❌ {path} 不存在")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 最简单的测试内容
    test_content = """
# 测试

/答案是2。/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("\n生成测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_fix_simple.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF:")
                print("1. 是否有答案框（圆角矩形背景）")
                print("2. '答案'是否显示为橙色标签图片")
                print("3. 如果'答案'消失但没有图片，说明路径问题")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_fix())
