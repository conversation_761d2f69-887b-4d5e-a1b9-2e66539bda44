<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答案图片突出显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .evolution {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .evolution-item {
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-size: 14px;
        }
        .v1 { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .v2 { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        .v3 { background-color: #e2e3e5; border: 1px solid #d6d8db; }
        .v4 { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 答案图片突出显示测试</h1>
        
        <div class="test-section">
            <h3>📈 答案图片位置演进</h3>
            <div class="evolution">
                <div class="evolution-item v1">
                    <h4>V1 🔄</h4>
                    <p>居中显示</p>
                    <small>图片在答案框中央</small>
                </div>
                <div class="evolution-item v2">
                    <h4>V2 📍</h4>
                    <p>左对齐</p>
                    <small>图片对齐内容区域</small>
                </div>
                <div class="evolution-item v3">
                    <h4>V3 🎯</h4>
                    <p>贴住边框</p>
                    <small>图片贴住答案框边框</small>
                </div>
                <div class="evolution-item v4">
                    <h4>V4 ✅</h4>
                    <p><strong>突出显示</strong></p>
                    <small>图片突出到答案框外面</small>
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <h4>🎯 当前效果（V4）</h4>
            <p><strong>答案图片现在突出到答案框的左边外面！</strong></p>
            <ul>
                <li>使用负坐标：<code>img_x = -10px</code></li>
                <li>突出距离：10像素</li>
                <li>视觉效果：图片"悬浮"在答案框外</li>
                <li>更加醒目和突出</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📝 测试内容</h3>
            <textarea id="testContent"># 答案突出显示测试

## 测试题目

计算：5 × 6 = ?

/
这是一个乘法题目。

答案是30。

现在的"答案"图片应该突出到答案框的左边外面，比答案框本身更靠左。
/</textarea>
            
            <button onclick="testOutsideAlignment()">测试答案突出显示</button>
            <div id="testStatus"></div>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 检查要点</h3>
            <ul>
                <li><strong>✅ 突出显示：</strong> 答案图片应该突出到答案框的左边外面</li>
                <li><strong>✅ 负坐标：</strong> 图片使用负X坐标(-10px)</li>
                <li><strong>✅ 更醒目：</strong> 答案图片比答案框本身更靠左</li>
                <li><strong>✅ 统一效果：</strong> 所有答案图片都保持这种突出效果</li>
                <li><strong>⚠️ 边距适配：</strong> 页面左边距需要足够容纳突出的图片</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>💡 技术实现</h3>
            <p><strong>代码修改：</strong></p>
            <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
# 修改前（V3）
img_x = self.border_width  # 贴住边框

# 修改后（V4）
img_x = -10  # 突出到答案框外面10px</pre>
            
            <p><strong>配置调整：</strong></p>
            <ul>
                <li>增加左边距到3.0cm以容纳突出的答案图片</li>
                <li>确保答案图片不会与页面边界重叠</li>
                <li>保持其他边距不变</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        function showResult(content) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="result">${content}</div>`;
        }
        
        async function testOutsideAlignment() {
            const content = document.getElementById('testContent').value.trim();
            
            if (!content) {
                showStatus('请输入测试内容', 'error');
                return;
            }
            
            showStatus('正在生成PDF测试...', 'info');
            
            const requestData = {
                content: content,
                layout_config: {
                    page_format: "A4",
                    margin_top: 2.0,
                    margin_bottom: 2.0,
                    margin_left: 3.0,  // 增加左边距以容纳突出的答案图片
                    margin_right: 2.0,
                    font_size: 12,
                    line_height: 1.5,
                    paragraph_spacing: 12,
                    indent_first_line: true
                },
                filename: "answer_outside_test.pdf"
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/pdf/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('✅ PDF生成成功！', 'success');
                    
                    const downloadUrl = `${API_BASE}${result.pdf_url}`;
                    showResult(`
                        <h4>🚀 答案突出显示测试完成！</h4>
                        <p><strong>文件大小：</strong> ${result.file_size} 字节</p>
                        <p><strong>生成时间：</strong> ${result.generation_time.toFixed(2)} 秒</p>
                        <p><a href="${downloadUrl}" target="_blank" style="color: #007bff; text-decoration: none;">📥 点击下载PDF文件</a></p>
                        <div style="margin-top: 15px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                            <strong>🔍 检查答案图片突出效果：</strong><br>
                            1. 打开下载的PDF文件<br>
                            2. 查找答案框（圆角矩形背景）<br>
                            3. 检查"答案"图片是否突出到答案框外面<br>
                            4. 确认图片比答案框本身更靠左<br>
                            5. 验证图片使用负坐标(-10px)<br><br>
                            <strong>✅ 如果答案图片突出到答案框外面，说明修改成功！</strong><br>
                            <strong>🎯 这是最左的位置，答案图片现在比答案框还要靠左！</strong>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    showStatus('❌ PDF生成失败', 'error');
                    showResult(`<h4>错误信息：</h4><pre>${errorText}</pre>`);
                }
                
            } catch (error) {
                showStatus('❌ 网络请求失败', 'error');
                showResult(`<h4>错误详情：</h4><pre>${error.message}</pre>`);
            }
        }
    </script>
</body>
</html>
