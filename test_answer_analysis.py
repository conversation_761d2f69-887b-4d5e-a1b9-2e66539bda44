#!/usr/bin/env python3
"""
测试答案及解析样式的脚本
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_analysis_style():
    """测试答案及解析样式"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 数学练习题

## 第一题

计算下列表达式的值：2 + 3 × 4 = ?

/这是一个关于运算顺序的题目。根据数学运算法则，乘法的优先级高于加法，所以应该先计算3×4=12，然后计算2+12=14。因此答案是14。这个题目考查的是学生对基本运算顺序的掌握程度。/

## 第二题

如果一个正方形的边长是5cm，那么它的面积是多少？

/正方形的面积公式是：面积 = 边长 × 边长。已知边长为5cm，所以面积 = 5 × 5 = 25平方厘米。这是一个基础的几何计算题，主要考查学生对正方形面积公式的理解和应用。/

## 第三题

一个班级有30名学生，其中男生占60%，女生有多少人？

/首先计算男生人数：30 × 60% = 30 × 0.6 = 18人。然后计算女生人数：30 - 18 = 12人。或者直接计算：30 × (100% - 60%) = 30 × 40% = 12人。这道题考查的是百分比的计算和应用。/

这是一个普通段落，用来测试答案及解析框与普通文本的区别。

/这是另一个答案及解析的例子，用来测试多个答案框在同一个文档中的显示效果。内容可以很长，系统会自动换行并调整框的高度以适应内容。这样可以确保所有文字都能完整显示在框内。/

## 总结

通过以上练习，学生可以巩固基础的数学计算能力。
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_analysis.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("请检查生成的PDF文件，验证答案及解析框的样式是否正确。")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_analysis_style())
