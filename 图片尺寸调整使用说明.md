# 图片尺寸调整使用说明

## 概述

我已经为你的PDF生成系统的"答案及解析"样式添加了完整的图片尺寸调整功能。现在你可以通过多种方式控制答案框中图片的显示尺寸。

## 功能特性

### 🎯 支持的调整方式

1. **预设尺寸选项**：small, medium, large, original
2. **精确尺寸控制**：指定具体的宽度和/或高度
3. **自动适应**：系统自动计算合适的尺寸
4. **比例保持**：自动保持图片的原始宽高比
5. **边界限制**：确保图片不会超出答案框范围

### 📝 语法格式

#### 基本语法
```markdown
![图片描述](图片路径?参数=值&参数=值)
```

#### 支持的参数

| 参数 | 说明 | 示例值 | 效果 |
|------|------|--------|------|
| `size` | 预设尺寸 | `small`, `medium`, `large`, `original` | 使用预定义的尺寸规则 |
| `width` | 指定宽度 | `150`, `200`, `300` | 设置图片宽度（像素） |
| `height` | 指定高度 | `100`, `150`, `200` | 设置图片高度（像素） |

## 使用示例

### 1. 预设尺寸选项

#### 小尺寸 (size=small)
```markdown
/
这是一个小尺寸的图片示例：

![小图标](images/icon.png?size=small)

适合用作图标或简单示意图。
/
```

#### 中等尺寸 (size=medium)
```markdown
/
这是一个中等尺寸的图片示例：

![教学图片](images/diagram.png?size=medium)

适合大多数教学场景，推荐使用。
/
```

#### 大尺寸 (size=large)
```markdown
/
这是一个大尺寸的图片示例：

![详细图表](images/chart.png?size=large)

适合需要详细展示的重要图表。
/
```

#### 原始尺寸 (size=original)
```markdown
/
这是原始尺寸的图片示例：

![高质量图片](images/photo.png?size=original)

保持图片原始比例，但不超出答案框宽度。
/
```

### 2. 精确尺寸控制

#### 指定宽度
```markdown
/
指定图片宽度为200像素：

![指定宽度](images/example.png?width=200)

高度会按比例自动调整。
/
```

#### 指定高度
```markdown
/
指定图片高度为150像素：

![指定高度](images/example.png?height=150)

宽度会按比例自动调整。
/
```

#### 同时指定宽度和高度
```markdown
/
同时指定宽度和高度：

![指定宽高](images/example.png?width=250&height=180)

注意：可能会导致图片变形，请谨慎使用。
/
```

### 3. 默认自动尺寸
```markdown
/
不指定任何参数，使用默认自动尺寸：

![自动尺寸](images/example.png)

系统会自动计算合适的显示尺寸。
/
```

## 尺寸规则详解

### 预设尺寸规则

| 尺寸选项 | 宽度比例 | 最大高度 | 适用场景 |
|----------|----------|----------|----------|
| `small` | 40% | 80px | 图标、简单示意图 |
| `medium` | 70% | 120px | 一般教学图片（推荐） |
| `large` | 90% | 180px | 重要图表、复杂图形 |
| `original` | 原始 | 不限 | 高质量图片展示 |
| 默认 | 自适应 | 150px | 自动平衡尺寸 |

### 精确控制规则

1. **只指定宽度**：高度按原始比例自动计算
2. **只指定高度**：宽度按原始比例自动计算
3. **同时指定宽高**：使用指定的精确尺寸（可能变形）

### 边界限制

- 图片宽度不会超过答案框的可用宽度
- 答案框高度会根据内容自动调整
- 过大的内容会被限制在页面可用高度的80%以内

## 最佳实践

### 🎯 推荐用法

1. **一般情况**：使用 `size=medium`，适合大多数教学场景
2. **图标类**：使用 `size=small`，节省空间
3. **重要图表**：使用 `size=large`，突出显示
4. **高质量图片**：使用 `size=original`，保持原始效果
5. **特殊需求**：使用 `width` 或 `height` 精确控制

### ⚠️ 注意事项

1. **避免变形**：尽量不要同时指定宽度和高度
2. **保持一致**：在同一文档中尽量使用统一的尺寸规则
3. **考虑内容**：根据图片内容选择合适的尺寸
4. **测试效果**：生成PDF后检查显示效果

### 📋 常见用例

#### 数学教学
```markdown
/
根据下图计算面积：

![几何图形](images/geometry.png?size=medium)

长方形面积 = 长 × 宽 = 8 × 5 = 40平方厘米
/
```

#### 步骤说明
```markdown
/
计算步骤如下：

![计算过程](images/steps.png?size=large)

按照图中步骤依次计算即可得到答案。
/
```

#### 图标说明
```markdown
/
注意事项：

![警告图标](images/warning.png?size=small) 请仔细检查计算过程

这个小图标用来提醒重要信息。
/
```

## 技术实现

### 处理流程

1. **解析语法**：从Markdown语法中提取图片路径和参数
2. **参数解析**：解析尺寸参数（size, width, height）
3. **图片加载**：使用PIL加载图片并获取原始尺寸
4. **尺寸计算**：根据参数和规则计算新尺寸
5. **图片创建**：创建ReportLab Image对象
6. **布局渲染**：在答案框中居中显示图片

### 兼容性

- ✅ 向后兼容：不带参数的图片语法仍然正常工作
- ✅ 错误处理：参数解析失败时使用默认尺寸
- ✅ 文件支持：支持PNG、JPG、GIF等常见格式
- ✅ 路径支持：支持相对路径和绝对路径

## 总结

图片尺寸调整功能为教学材料制作提供了强大而灵活的控制能力：

- **简单易用**：通过URL参数的方式控制尺寸
- **功能丰富**：支持预设和精确两种控制方式
- **自动优化**：智能计算合适的显示尺寸
- **视觉统一**：保持答案框的整体美观效果

现在你可以根据不同的教学需求，灵活调整答案框中图片的显示效果，让教学材料更加生动和专业！
