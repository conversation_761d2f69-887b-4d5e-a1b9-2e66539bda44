#!/usr/bin/env python3
"""
测试答案图片左对齐效果
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_answer_left_align():
    """测试答案图片左对齐效果"""
    
    print("🔍 测试答案图片左对齐效果...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 创建测试内容，包含多种答案使用场景
    test_content = """
# 答案图片左对齐测试

## 第一题：基础测试

计算：5 + 3 = ?

/
这是一个简单的加法题目。

答案是8。

这里的"答案"图片应该左对齐贴住答案框边缘。
/

## 第二题：多个答案测试

下列哪些是偶数？A.2 B.3 C.4 D.5

/
偶数是能被2整除的数字。

**分析过程：**
- A. 2是偶数，答案正确
- B. 3不是偶数
- C. 4是偶数，答案正确  
- D. 5不是偶数

**最终答案：** A和C

所有的"答案"图片都应该左对齐显示。
/

## 第三题：长文本测试

小明有15个苹果，吃了5个，还剩多少个？

/
这是一个减法应用题，需要仔细分析。

**已知条件：**
- 小明原来有15个苹果
- 吃了5个苹果

**计算过程：**
15 - 5 = 10

**答案：** 10个苹果

这道题考查学生的减法运算能力。答案很明确，就是10个苹果。

通过这个例子可以看到，即使在长文本中，答案图片也应该保持左对齐，不会因为文本长度而居中显示。
/

## 第四题：格式化文本测试

计算正方形面积：边长 = 4cm

/
正方形面积计算公式：

**面积 = 边长 × 边长**

**代入数值：**
面积 = 4 × 4 = 16平方厘米

**答案：** 16平方厘米

这里的答案图片应该与左边的文字对齐，而不是居中显示。

**验证：** 4² = 16，答案正确。
/

## 第五题：对比测试（无答案字样）

计算：10 ÷ 2 = ?

/
这是一个简单的除法题目。

10 ÷ 2 = 5

结果是5。这道题比较简单，学生应该能够快速计算出结果。

这个答案框中没有"答案"字样，所以不会有图片替换，可以作为对比。
/

## 第六题：混合内容测试

计算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要按照运算顺序来计算。

**第一步：** 计算括号内容
(8 + 4) = 12

**第二步：** 计算乘除法（从左到右）
12 × 3 = 36
6 ÷ 2 = 3

**第三步：** 计算减法
36 - 3 = 33

**最终答案：** 33

运算顺序很重要：先括号，再乘除，最后加减。

这里的答案图片应该左对齐，与前面的文字保持一致的对齐方式。

**总结：** 答案是33，计算过程正确。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成答案左对齐测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_left_align.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF中的答案图片对齐效果:")
                print("1. ✅ 答案图片应该左对齐，贴住答案框的左边缘")
                print("2. ✅ 答案图片不应该居中显示")
                print("3. ✅ 答案图片与前后文字的对齐方式一致")
                print("4. ✅ 多个答案图片都保持左对齐")
                print("5. ✅ 长文本中的答案图片也保持左对齐")
                
                print("\n对比效果:")
                print("- 🔄 修改前：答案图片居中显示")
                print("- ✅ 修改后：答案图片左对齐贴边")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_answer_left_align())
