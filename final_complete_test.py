#!/usr/bin/env python3
"""
最终完整功能测试 - 展示所有样式功能的完整使用
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def final_complete_test():
    """最终完整功能测试"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建综合测试内容
    test_content = """
# 小学数学综合练习册（完整版）

## 第一章：基础运算

### 1.1 加减法练习

1. 计算：25 + 37 = ?

/
这是一个两位数加法题目。

**计算过程：**
  25
+ 37
----
  62

**答案：** 62

这道题考查学生对两位数加法的掌握。（（加法是数学运算的基础））
/

2. 计算：83 - 29 = ?

/
这是一个需要借位的减法题目。

**计算过程：**
- 个位：3 - 9 不够减，需要从十位借1
- 个位变成：13 - 9 = 4  
- 十位：8 - 1 - 2 = 5

**答案：** 54

借位减法是减法运算中的重要技巧。
/

### 1.2 乘除法练习

3. 计算：6 × 8 = ?

/
这是九九乘法表中的基础题目。

**答案：** 48

熟练掌握乘法表是数学学习的基础。
/

4. 计算：144 ÷ 12 = ?

/
这是一个整除的除法题目。

![计算步骤](test_images/calculation_steps.png?size=medium)

**方法一：** 直接计算 144 ÷ 12 = 12
**方法二：** 分解计算 144 = 12 × 12

**答案：** 12

验证：12 × 12 = 144 ✓
/

## 第二章：几何图形

### 2.1 面积计算

5. 计算下图长方形的面积和周长：

/
根据图中给出的尺寸：

![长方形](test_images/rectangle.png?size=large)

**面积计算：**
面积 = 长 × 宽 = 8cm × 5cm = 40平方厘米

**周长计算：**  
周长 = 2 × (长 + 宽) = 2 × (8 + 5) = 26厘米

**答案：**
- 面积：40平方厘米
- 周长：26厘米

这道题考查学生对长方形基本公式的应用。
/

6. 如果一个正方形的面积是25平方厘米，求它的边长。

/
这是一个逆向思维的几何题目。

**已知：** 正方形面积 = 25平方厘米

**求解过程：**
∵ 正方形面积 = 边长²
∴ 边长² = 25
∴ 边长 = √25 = 5厘米

**答案：** 5厘米

验证：5² = 25 ✓（（平方根是面积计算的逆运算））
/

## 第三章：应用题

### 3.1 购物问题

7. 小明买文具，买了3支铅笔（每支2元）和2块橡皮（每块1.5元），一共需要多少钱？

/
这是一个多步骤的应用题。

**第一步：** 计算铅笔总价
铅笔总价 = 3 × 2 = 6元

**第二步：** 计算橡皮总价
橡皮总价 = 2 × 1.5 = 3元

**第三步：** 计算总花费
总花费 = 6 + 3 = 9元

**答案：** 9元

这道题考查学生的分步计算能力和小数乘法。
/

### 3.2 时间问题

8. 如果现在是上午9:15，再过2小时30分钟是几点？

/
这是一个时间加法问题。

**计算过程：**
起始时间：9:15
增加时间：2小时30分钟

**分步计算：**
1. 先加小时：9:15 + 2小时 = 11:15
2. 再加分钟：11:15 + 30分钟 = 11:45

**答案：** 11:45（上午11点45分）

时间计算要注意分钟满60进位到小时。
/

## 第四章：分数与小数

### 4.1 分数计算

9. 计算：1/2 + 1/4 = ?

/
这是一个分数加法题目，需要通分。

**通分过程：**
1/2 = 2/4
1/4 = 1/4

**相加：**
2/4 + 1/4 = 3/4

**答案：** 3/4

分数加法的关键是找到公分母进行通分。
/

### 4.2 小数计算

10. 计算：3.5 + 2.8 = ?

/
小数加法要注意小数点对齐。

**竖式计算：**
  3.5
+ 2.8
-----
  6.3

**答案：** 6.3

小数加法与整数加法类似，关键是小数点对齐。
/

## 第五章：函数与图像

### 5.1 函数图像识别

11. 观察下面的函数图像，说明这是什么函数？

/
这是一个二次函数的图像：

![数学图表](test_images/math_diagram.png?size=large)

**函数特点：**
1. 开口向上的抛物线
2. 顶点在原点 (0,0)
3. 关于y轴对称

**答案：** y = x² 函数

这是最基本的二次函数形式。（（二次函数在中学数学中很重要））
/

## 综合练习

### 综合题

12. 综合运算：(8 + 4) × 3 - 6 ÷ 2 = ?

/
这道题需要严格按照运算顺序计算：

![计算步骤](test_images/calculation_steps.png?size=small)

**运算顺序：** 括号 → 乘除 → 加减

**详细步骤：**
1. 计算括号：(8 + 4) = 12
2. 计算乘法：12 × 3 = 36
3. 计算除法：6 ÷ 2 = 3  
4. 计算减法：36 - 3 = 33

**答案：** 33

运算顺序是数学计算的基础规则。
/

## 总结

/
通过这套综合练习，我们复习了小学数学的主要内容：

**基础运算：**
- 加减法（包括借位运算）
- 乘除法（包括九九乘法表）

**几何图形：**
- 长方形和正方形的面积、周长计算
- 逆向思维题目

**应用题：**
- 购物计算问题  
- 时间计算问题

**分数与小数：**
- 分数的通分和加法
- 小数的加法运算

**函数图像：**
- 二次函数的识别和特点

![数学图表](test_images/math_diagram.png?size=medium)

**学习建议：**
1. 熟练掌握基本运算法则
2. 理解几何图形的基本公式
3. 培养解决实际问题的能力
4. 注意运算顺序和计算准确性

**答案：** 数学学习需要循序渐进，打好基础很重要！

所有的答案都已经用橙色标签标注，便于学生识别和复习。（（答案标注让学习更加高效））
/
"""
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        # 生成PDF
        print("正在生成最终完整功能测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="final_complete_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print("🎉 最终完整功能测试完成！")
        print("📋 包含的完整功能：")
        print("   ✅ 一级标题（带背景图片）")
        print("   ✅ 二级和三级标题")
        print("   ✅ 编号列表（带圆形背景）")
        print("   ✅ 答案及解析框（文本内容）")
        print("   ✅ 答案及解析框（图片显示）")
        print("   ✅ 答案及解析框（图文混合）")
        print("   ✅ 图片尺寸调整（size参数）")
        print("   ✅ '答案'文字替换为图片")
        print("   ✅ 双括号文本（橙色楷体）")
        print("   ✅ 粗体和格式化文本")
        print("   ✅ 图片自动缩放和居中")
        print("   ✅ 自动换行和高度调整")
        print("   ✅ 所有样式的完美融合")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(final_complete_test())
