#!/usr/bin/env python3
"""
测试缩小50%后的答案图片效果
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_smaller_answer_image():
    """测试缩小50%后的答案图片效果"""
    
    print("🔍 测试缩小50%后的答案图片效果...")
    
    # 创建PDF服务
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 答案图片缩小测试

## 尺寸对比说明

计算：4 × 5 = ?

/
这是一个乘法题目。

答案是20。

现在的"答案"图片已经缩小50%，从80×38.7像素缩小到40×19.4像素。
/

## 尺寸变化详情

/
**原始设置：**
- 最大宽度：80像素
- 宽度比例：答案框宽度的20%
- 实际显示：80×38.7像素

**新设置（缩小50%）：**
- 最大宽度：40像素
- 宽度比例：答案框宽度的10%
- 实际显示：40×19.4像素

**变化效果：**
- 宽度减少：80px → 40px
- 高度减少：38.7px → 19.4px
- 面积减少：75% (缩小50%意味着面积减少75%)

这样的答案图片更加精致，不会过分占用版面空间。
/

## 多个答案测试

第一题：6 + 7 = ?

/
加法运算。

答案：13
/

第二题：15 ÷ 3 = ?

/
除法运算。

答案：5
/

第三题：2³ = ?

/
幂运算。

计算：2 × 2 × 2 = 8

答案：8
/

## 视觉效果对比

/
**缩小前的问题：**
- 答案图片过大，占用过多空间
- 视觉上过于突出，影响阅读流畅性
- 与文字比例不协调

**缩小后的优势：**
- 答案图片大小适中，更加精致
- 与文字大小比例更协调
- 保持醒目效果的同时不影响阅读
- 节省版面空间，布局更紧凑

**技术参数变化：**
- target_width: min(80, max_width * 0.2) → min(40, max_width * 0.1)
- 缩放比例：从0.38降低到0.19
- 突出位置：仍然是-10px（保持突出效果）

所有的"答案"图片现在都使用新的较小尺寸，整体视觉效果更加平衡。
/

## 长文本测试

复杂应用题：一个长方形的长是8厘米，宽是5厘米，求它的周长和面积。

/
这是一个几何应用题，需要分别计算周长和面积。

**已知条件：**
- 长方形的长：8厘米
- 长方形的宽：5厘米

**计算周长：**
周长公式：C = 2(长 + 宽)
C = 2(8 + 5) = 2 × 13 = 26厘米

**计算面积：**
面积公式：S = 长 × 宽
S = 8 × 5 = 40平方厘米

**最终答案：**
- 周长：26厘米
- 面积：40平方厘米

这道题考查学生对长方形周长和面积公式的掌握。

即使在长文本中，缩小后的答案图片也能保持良好的视觉效果，不会显得过于突兀。
/
"""
    
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=3.0,  # 保持较大左边距以容纳突出的答案图片
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=True
    )
    
    try:
        print("生成缩小答案图片测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="test_answer_smaller.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 自动打开PDF
            import subprocess
            import platform
            system = platform.system()
            if system == "Darwin":  # macOS
                subprocess.run(["open", pdf_path])
                print("🔍 已自动打开PDF文件")
                
                print("\n请检查PDF中缩小后的答案图片效果:")
                print("1. ✅ 答案图片应该比之前小50%")
                print("2. ✅ 新尺寸：40×19.4像素（原来80×38.7像素）")
                print("3. ✅ 图片仍然突出到答案框外面")
                print("4. ✅ 与文字比例更加协调")
                print("5. ✅ 视觉效果更加精致")
                
                print("\n尺寸变化对比:")
                print("- 🔄 修改前：80×38.7像素")
                print("- ✅ 修改后：40×19.4像素")
                print("- 📉 缩小比例：50%")
                print("- 📊 面积减少：75%")
                
                print("\n技术实现:")
                print("- 最大宽度：80px → 40px")
                print("- 宽度比例：20% → 10%")
                print("- 位置：仍然突出-10px")
                print("- 间距：仍然5px")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_smaller_answer_image())
